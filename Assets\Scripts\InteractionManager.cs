using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// 交互管理器 - 管理设备交互和UI显示
/// </summary>
public class InteractionManager : MonoBehaviour
{
    [Header("设备控制")]
    [Tooltip("设备控制器引用")]
    public DeviceController deviceController;
    
    [Header("UI组件")]
    [Tooltip("功能区域数据面板")]
    public GameObject dataPanel;
    
    [Tooltip("数据面板标题")]
    public Text dataPanelTitle;
    
    [Tooltip("数据面板内容")]
    public Text dataPanelContent;
    
    [Header("功能区域")]
    [Tooltip("可交互的功能区域列表")]
    public List<FunctionalArea> functionalAreas = new List<FunctionalArea>();
    
    // 当前选中的功能区域
    private FunctionalArea currentArea;
    
    [System.Serializable]
    public class FunctionalArea
    {
        public string areaName;
        public Transform areaTransform;
        [TextArea(3, 5)]
        public string areaDescription;
        public string[] areaData;
    }
    
    void Start()
    {
        // 初始化隐藏数据面板
        if (dataPanel != null)
        {
            dataPanel.SetActive(false);
        }
        
        // 确保设备控制器引用有效
        if (deviceController == null)
        {
            deviceController = FindObjectOfType<DeviceController>();
            if (deviceController == null)
            {
                Debug.LogError("未找到DeviceController组件，请手动设置引用");
            }
        }
    }
    
    void Update()
    {
        // 检测点击事件
        if (Input.GetMouseButtonDown(0))
        {
            Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
            RaycastHit hit;
            
            if (Physics.Raycast(ray, out hit))
            {
                // 检查是否点击了功能区域
                CheckFunctionalAreaClick(hit.transform);
            }
            else
            {
                // 点击空白区域隐藏数据面板
                HideDataPanel();
            }
        }
        
        // ESC键隐藏数据面板
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            HideDataPanel();
        }
    }
    
    /// <summary>
    /// 检查是否点击了功能区域
    /// </summary>
    private void CheckFunctionalAreaClick(Transform clickedTransform)
    {
        foreach (FunctionalArea area in functionalAreas)
        {
            if (area.areaTransform == clickedTransform || IsChildOf(clickedTransform, area.areaTransform))
            {
                ShowAreaData(area);
                currentArea = area;
                return;
            }
        }
    }
    
    /// <summary>
    /// 检查是否是父物体的子物体
    /// </summary>
    private bool IsChildOf(Transform child, Transform parent)
    {
        Transform current = child;
        while (current != null)
        {
            if (current == parent)
            {
                return true;
            }
            current = current.parent;
        }
        return false;
    }
    
    /// <summary>
    /// 显示区域数据
    /// </summary>
    private void ShowAreaData(FunctionalArea area)
    {
        if (dataPanel != null && dataPanelTitle != null && dataPanelContent != null)
        {
            // 设置面板标题和描述
            dataPanelTitle.text = area.areaName;
            
            // 构建数据内容
            System.Text.StringBuilder sb = new System.Text.StringBuilder();
            sb.AppendLine(area.areaDescription);
            sb.AppendLine();
            
            if (area.areaData != null && area.areaData.Length > 0)
            {
                sb.AppendLine("数据信息：");
                foreach (string data in area.areaData)
                {
                    sb.AppendLine("- " + data);
                }
            }
            
            dataPanelContent.text = sb.ToString();
            
            // 显示面板
            dataPanel.SetActive(true);
            
            // 可以添加面板显示动画
            StartCoroutine(AnimatePanelShow());
        }
    }
    
    /// <summary>
    /// 隐藏数据面板
    /// </summary>
    public void HideDataPanel()
    {
        if (dataPanel != null && dataPanel.activeSelf)
        {
            StartCoroutine(AnimatePanelHide());
        }
    }
    
    /// <summary>
    /// 数据面板显示动画
    /// </summary>
    private IEnumerator AnimatePanelShow()
    {
        CanvasGroup canvasGroup = dataPanel.GetComponent<CanvasGroup>();
        if (canvasGroup == null)
        {
            canvasGroup = dataPanel.AddComponent<CanvasGroup>();
        }
        
        canvasGroup.alpha = 0f;
        
        float duration = 0.3f;
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = Mathf.Clamp01(elapsed / duration);
            canvasGroup.alpha = Mathf.SmoothStep(0, 1, t);
            yield return null;
        }
        
        canvasGroup.alpha = 1f;
    }
    
    /// <summary>
    /// 数据面板隐藏动画
    /// </summary>
    private IEnumerator AnimatePanelHide()
    {
        CanvasGroup canvasGroup = dataPanel.GetComponent<CanvasGroup>();
        if (canvasGroup == null)
        {
            canvasGroup = dataPanel.AddComponent<CanvasGroup>();
            canvasGroup.alpha = 1f;
        }
        
        float duration = 0.2f;
        float elapsed = 0f;
        
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = Mathf.Clamp01(elapsed / duration);
            canvasGroup.alpha = Mathf.SmoothStep(1, 0, t);
            yield return null;
        }
        
        canvasGroup.alpha = 0f;
        dataPanel.SetActive(false);
    }
}