using UnityEngine;

/// <summary>
/// Unity与WebGL页面通信的桥接插件
/// </summary>
public static class ChartPlugin
{
    [System.Runtime.InteropServices.DllImport("__Internal")]
    private static extern void InitChartsInternal();
    /// <summary>
    /// 初始化图表，调用WebGL页面中的initCharts函数
    /// </summary>
    public static void InitCharts()
    {
        Debug.Log("[ChartPlugin] 开始初始化图表...");
#if UNITY_WEBGL && !UNITY_EDITOR
        try
        {
            InitChartsInternal();
            Debug.Log("[ChartPlugin] 图表初始化完成");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[ChartPlugin] 图表初始化失败: {e.Message}");
        }
#else
        Debug.Log("[ChartPlugin] 非WebGL平台，跳过图表初始化");
#endif
    }
}