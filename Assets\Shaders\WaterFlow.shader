Shader "Custom/WaterFlow" {
    Properties {
        _Color ("Main Color", Color) = (0.2, 0.5, 0.8, 0.8)
        _MainTex ("Water Texture", 2D) = "white" {}
        _Glossiness ("Smoothness", Range(0, 1)) = 0.8
        _Metallic ("Metallic", Range(0, 1)) = 0.0
    }
    
    SubShader {
        Tags { "RenderType"="Transparent" "Queue"="Transparent" }
        LOD 200
        
        CGPROGRAM
        #pragma surface surf Standard fullforwardshadows alpha:fade
        #pragma target 3.0
        
        sampler2D _MainTex;
        fixed4 _Color;
        half _Glossiness;
        half _Metallic;
        
        struct Input {
            float2 uv_MainTex;
        };
        
        void surf (Input IN, inout SurfaceOutputStandard o) {
            // 使用原始UV坐标，不添加流动效果
            float2 flowUV = IN.uv_MainTex;
            
            // 采样纹理并应用颜色
            fixed4 c = tex2D (_MainTex, flowUV) * _Color;
            o.Albedo = c.rgb;
            o.Metallic = _Metallic;
            o.Smoothness = _Glossiness;
            o.Alpha = c.a;
        }
        ENDCG
    }
    FallBack "Diffuse"
}