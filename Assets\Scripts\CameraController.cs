using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.InteropServices;

/// <summary>
/// 摄像机控制器，实现摄像机的移动、旋转和缩放功能
/// </summary>
public class CameraController : MonoBehaviour
{
    // WebGL与JavaScript交互接口
    [DllImport("__Internal")]
    private static extern void HideEchartsInternal();
    
    [DllImport("__Internal")]
    private static extern void ShowEchartsInternal();
    
    /// <summary>
    /// 在WebGL中隐藏echarts图表
    /// </summary>
    private void HideEchartsInWebGL()
    {
#if UNITY_WEBGL && !UNITY_EDITOR
        try
        {
            HideEchartsInternal();
            Debug.Log("[CameraController] 已调用JavaScript函数隐藏echarts图表");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[CameraController] 隐藏echarts图表失败: {e.Message}");
        }
#else
        Debug.Log("[CameraController] 非WebGL平台，跳过隐藏echarts图表");
#endif
    }
    
    /// <summary>
    /// 在WebGL中显示echarts图表
    /// </summary>
    private void ShowEchartsInWebGL()
    {
#if UNITY_WEBGL && !UNITY_EDITOR
        try
        {
            ShowEchartsInternal();
            Debug.Log("[CameraController] 已调用JavaScript函数显示echarts图表");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[CameraController] 显示echarts图表失败: {e.Message}");
        }
#else
        Debug.Log("[CameraController] 非WebGL平台，跳过显示echarts图表");
#endif
    }
    
    [Header("移动设置")]
    [Tooltip("摄像机移动速度")]
    public float moveSpeed = 5.0f;
    [Tooltip("按住Shift键时的加速倍率")]
    public float sprintMultiplier = 2.0f;

    [Header("旋转设置")]
    [Tooltip("摄像机水平旋转灵敏度")]
    public float horizontalRotationSpeed = 2.0f;
    [Tooltip("摄像机垂直旋转灵敏度")]
    public float verticalRotationSpeed = 2.0f;
    [Tooltip("垂直视角限制的最小角度")]
    public float minVerticalAngle = -80.0f;
    [Tooltip("垂直视角限制的最大角度")]
    public float maxVerticalAngle = 80.0f;

    [Header("缩放设置")]
    [Tooltip("摄像机缩放速度")]
    public float zoomSpeed = 0.0f;
    [Tooltip("最小视野角度")]
    public float minFieldOfView = 10.0f;
    [Tooltip("最大视野角度")]
    public float maxFieldOfView = 90.0f;
    
    [Header("平移设置")]
    [Tooltip("鼠标中键平移速度")]
    public float panSpeed = 1.0f;
    
    [Header("位置管理")]
    [Tooltip("总览初始位置")]
    public Transform firstCameraInitialPosition;
    [Tooltip("设备视角位置列表")]
    public List<Transform> deviceCameraPositions = new List<Transform>();
    [Tooltip("相机位置切换速度")]
    public float positionTransitionSpeed = 2.0f;
    [Tooltip("相机旋转切换速度")]
    public float rotationTransitionSpeed = 2.0f;
    [Tooltip("视野切换速度")]
    public float fovTransitionSpeed = 2.0f;
    [Tooltip("设备视角漫游间隔时间(秒)")]
    public float deviceViewDwellTime = 3.0f;
    
    [Tooltip("需要在漫游时控制显示/隐藏的Canvas列表")]
    [SerializeField]
    public List<Canvas> controlledCanvases = new List<Canvas>();
    
    [Tooltip("设备控制器")]
    public DeviceController deviceController;

    // 私有变量
    private Camera mainCamera;
    private float currentVerticalAngle = 0.0f;
    private bool isRotating = false;
    private bool isPanning = false;
    private Vector3 lastMousePosition;
    
    // 存储初始位置和旋转信息
    private Vector3 initialPosition;
    private Quaternion initialRotation;
    private float initialFieldOfView;
    private float middleClickTime;
    private bool isMiddleClick = false;
    private float clickThreshold = 0.2f; // 单击判定阈值（秒）
    
    // 位置管理变量
    private bool isTransitioning = false;
    private bool isTouringDeviceViews = false;
    private int currentDeviceViewIndex = -1;
    private Coroutine tourCoroutine;

    private void Start()
    {
        mainCamera = GetComponent<Camera>();
        if (mainCamera == null)
        {
            Debug.LogError("CameraController脚本必须挂载在带有Camera组件的物体上！");
            enabled = false;
            return;
        }

        // 初始化垂直角度
        currentVerticalAngle = transform.eulerAngles.x;
        // 确保角度在-180到180度之间
        if (currentVerticalAngle > 180)
        {
            currentVerticalAngle -= 360;
        }
        
        // 保存初始位置和旋转信息
        SaveInitialTransform();
        
        // 进入总览
        SwitchToOverviewPosition();
        // 调用WebGL页面中的initCharts函数
#if UNITY_WEBGL && !UNITY_EDITOR
        ChartPlugin.InitCharts();
#endif
    }
    
    /// <summary>
    /// 保存摄像机的初始位置和旋转信息
    /// </summary>
    private void SaveInitialTransform()
    {
        initialPosition = transform.position;
        initialRotation = transform.rotation;
        if (mainCamera != null)
        {
            initialFieldOfView = mainCamera.fieldOfView;
        }
    }

    private void Update()
    {
        // 如果正在过渡到新位置，不处理其他输入
        if (isTransitioning)
            return;
            
        HandleKeyboardMovement();
        HandleMouseRotation();
        HandleMouseZoom();
        HandleMiddleMousePan();
        HandlePositionManagement();
    }

    /// <summary>
    /// 处理键盘WASDQE移动
    /// </summary>
    private void HandleKeyboardMovement()
    {
        // 获取输入
        float horizontal = Input.GetAxis("Horizontal"); // A和D键
        float vertical = Input.GetAxis("Vertical");     // W和S键
        float upDown = 0;

        // Q和E键控制上下移动
        if (Input.GetKey(KeyCode.Q))
        {
            upDown -= 1.0f;
        }
        if (Input.GetKey(KeyCode.E))
        {
            upDown += 1.0f;
        }

        // 计算移动速度（按住Shift加速）
        float currentMoveSpeed = moveSpeed;
        if (Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift))
        {
            currentMoveSpeed *= sprintMultiplier;
        }

        // 计算移动方向
        Vector3 moveDirection = new Vector3(horizontal, upDown, vertical);
        
        // 前后移动和左右移动基于摄像机的朝向
        Vector3 forward = transform.forward;
        forward.y = 0; // 保持水平移动
        forward.Normalize();
        
        Vector3 right = transform.right;
        right.y = 0; // 保持水平移动
        right.Normalize();
        
        // 计算最终移动向量
        Vector3 movement = (right * moveDirection.x + forward * moveDirection.z + Vector3.up * moveDirection.y) * currentMoveSpeed * Time.deltaTime;
        
        // 应用移动
        transform.position += movement;
    }

    /// <summary>
    /// 处理鼠标左键旋转
    /// </summary>
    private void HandleMouseRotation()
    {
        // 检测鼠标左键按下状态
        if (Input.GetMouseButtonDown(0))
        {
            isRotating = true;
            lastMousePosition = Input.mousePosition;
        }
        else if (Input.GetMouseButtonUp(0))
        {
            isRotating = false;
        }
        else if (!Input.GetMouseButton(0))
        {
            // 确保在鼠标左键释放后重置旋转状态
            isRotating = false;
            return;
        }

        // 如果正在旋转，更新lastMousePosition并应用旋转
        if (isRotating)
        {
            // 计算鼠标移动差值
            Vector3 mouseDelta = Input.mousePosition - lastMousePosition;
            lastMousePosition = Input.mousePosition;

            // 水平旋转（左右）
            float horizontalRotation = mouseDelta.x * horizontalRotationSpeed * Time.deltaTime;
            transform.Rotate(Vector3.up, horizontalRotation, Space.World);

            // 垂直旋转（上下）
            float verticalRotation = -mouseDelta.y * verticalRotationSpeed * Time.deltaTime;
            currentVerticalAngle = Mathf.Clamp(currentVerticalAngle + verticalRotation, minVerticalAngle, maxVerticalAngle);

            // 应用垂直旋转
            Vector3 currentRotation = transform.eulerAngles;
            transform.eulerAngles = new Vector3(currentVerticalAngle, currentRotation.y, 0);
        }
    }

    /// <summary>
    /// 处理鼠标滚轮缩放
    /// </summary>
    private void HandleMouseZoom()
    {
        // 获取鼠标滚轮输入
        float scrollDelta = Input.mouseScrollDelta.y;

        if (scrollDelta != 0 && mainCamera != null)
        {
            // 调整视野角度实现缩放效果
            float newFieldOfView = mainCamera.fieldOfView - scrollDelta * zoomSpeed * Time.deltaTime;
            mainCamera.fieldOfView = Mathf.Clamp(newFieldOfView, minFieldOfView, maxFieldOfView);
        }
    }
    
    /// <summary>
    /// 处理鼠标中键平移
    /// </summary>
    private void HandleMiddleMousePan()
    {
        // 检测鼠标中键按下状态
        if (Input.GetMouseButtonDown(2)) // 2代表鼠标中键
        {
            // 记录按下时间和位置，用于区分单击和拖动
            middleClickTime = Time.time;
            lastMousePosition = Input.mousePosition;
            isMiddleClick = true;
            isPanning = false;
        }
        else if (Input.GetMouseButtonUp(2))
        {
            isPanning = false;
            
            // 如果是单击（按下时间短且移动距离小）
            if (isMiddleClick && (Time.time - middleClickTime) < clickThreshold)
            {
                ResetCameraPosition();
            }
            
            isMiddleClick = false;
        }
        
        // 检测是否开始拖动（移动超过一定距离）
        if (isMiddleClick && !isPanning)
        {
            Vector3 mouseDelta = Input.mousePosition - lastMousePosition;
            if (mouseDelta.magnitude > 5.0f) // 移动超过5个像素判定为拖动
            {
                isPanning = true;
                isMiddleClick = false; // 不再是单击
            }
        }
        
        // 如果正在平移
        if (isPanning)
        {
            // 计算鼠标移动差值
            Vector3 mouseDelta = Input.mousePosition - lastMousePosition;
            lastMousePosition = Input.mousePosition;
            
            // 计算平移方向（与鼠标移动方向相反）
            Vector3 right = transform.right;
            Vector3 up = transform.up;
            
            // 计算平移向量
            Vector3 pan = (right * -mouseDelta.x + up * -mouseDelta.y) * panSpeed * Time.deltaTime;
            
            // 应用平移
            transform.position += pan;
        }
    }
    
    /// <summary>
    /// 重置摄像机到初始位置和旋转
    /// </summary>
    private void ResetCameraPosition()
    {
        transform.position = initialPosition;
        transform.rotation = initialRotation;
        if (mainCamera != null)
        {
            mainCamera.fieldOfView = initialFieldOfView;
        }
        
        // 重置垂直角度
        currentVerticalAngle = transform.eulerAngles.x;
        if (currentVerticalAngle > 180)
        {
            currentVerticalAngle -= 360;
        }
    }
    
    /// <summary>
    /// 切换到总览初始位置
    /// </summary>
    public void SwitchToOverviewPosition()
    {
        Debug.Log("[CameraController] 切换到总览初始位置");
        StopTourIfRunning();
        StartCoroutine(TransitionToPosition(firstCameraInitialPosition));
    }
    
    /// <summary>
    /// 处理位置管理相关的按键输入
    /// </summary>
    private void HandlePositionManagement()
    {
        // F1键：切换到总览初始位置
        if (Input.GetKeyDown(KeyCode.F1) && firstCameraInitialPosition != null)
        {
            SwitchToOverviewPosition();
        }
        
        // F2键：开始/终止设备视角漫游
        if (Input.GetKeyDown(KeyCode.F2))
        {
            ToggleDeviceViewTour();
        }
    }
    
    /// <summary>
    /// 切换设备视角漫游状态
    /// </summary>
    public void ToggleDeviceViewTour()
    {
        if (isTouringDeviceViews)
        {
            Debug.Log("[CameraController] 停止设备视角漫游");
            StopTourIfRunning();
        }
        else if (deviceCameraPositions.Count > 0)
        {
            Debug.Log("[CameraController] 开始设备视角漫游");
            StartDeviceViewTour();
        }
    }
    
    /// <summary>
    /// 停止正在进行的设备视角漫游
    /// </summary>
    private void StopTourIfRunning()
    {
        if (tourCoroutine != null)
        {
            StopCoroutine(tourCoroutine);
            tourCoroutine = null;
        }
        isTouringDeviceViews = false;
        
        // 漫游停止后隐藏Canvas，显示网页的echarts图表
#if UNITY_WEBGL && !UNITY_EDITOR
        // 调用JavaScript函数显示echarts图表
        ShowEchartsInWebGL();
#endif
        // 直接操作传入的Canvas列表，将它们隐藏
        foreach (Canvas canvas in controlledCanvases)
        {
            if (canvas != null)
            {
                CanvasGroup canvasGroup = canvas.GetComponent<CanvasGroup>();
                if (canvasGroup == null)
                {
                    canvasGroup = canvas.gameObject.AddComponent<CanvasGroup>();
                }
                canvasGroup.alpha = 0f; // 隐藏Canvas
                canvasGroup.interactable = false; // 禁用交互
                canvasGroup.blocksRaycasts = false; // 禁用射线检测
            }
        }
        
        Debug.Log("[CameraController] 漫游停止：隐藏Canvas，显示echarts图表");
    }
    
    /// <summary>
    /// 开始设备视角漫游
    /// </summary>
    private void StartDeviceViewTour()
    {
        if (deviceCameraPositions.Count == 0)
            return;
            
        StopTourIfRunning();
        isTouringDeviceViews = true;
        currentDeviceViewIndex = 0;
        
        // 漫游状态下隐藏网页的echarts图表，打开Canvas
#if UNITY_WEBGL && !UNITY_EDITOR
        // 调用JavaScript函数隐藏echarts图表
        HideEchartsInWebGL();
#endif
        // 直接操作传入的Canvas列表，将它们显示
        foreach (Canvas canvas in controlledCanvases)
        {
            if (canvas != null)
            {
                CanvasGroup canvasGroup = canvas.GetComponent<CanvasGroup>();
                if (canvasGroup == null)
                {
                    canvasGroup = canvas.gameObject.AddComponent<CanvasGroup>();
                }
                canvasGroup.alpha = 1f; // 显示Canvas
                canvasGroup.interactable = true; // 启用交互
                canvasGroup.blocksRaycasts = true; // 启用射线检测
            }
        }
        
        Debug.Log("[CameraController] 漫游开始：显示Canvas，隐藏echarts图表");
        
        tourCoroutine = StartCoroutine(TourDeviceViews());
    }
    
    /// <summary>
    /// 设备视角漫游协程
    /// </summary>
    private IEnumerator TourDeviceViews()
    {
        while (isTouringDeviceViews && deviceCameraPositions.Count > 0)
        {
            // 确保索引在有效范围内
            if (currentDeviceViewIndex >= deviceCameraPositions.Count)
                currentDeviceViewIndex = 0;
                
            // 获取当前视角位置
            Transform targetView = deviceCameraPositions[currentDeviceViewIndex];
            if (targetView != null)
            {
                Debug.Log($"[CameraController] 切换到设备视角 {currentDeviceViewIndex + 1}/{deviceCameraPositions.Count}");
                // 过渡到当前视角位置
                yield return StartCoroutine(TransitionToPosition(targetView));
                
                // 在当前视角停留指定时间
                yield return new WaitForSeconds(deviceViewDwellTime);
            }
            
            // 移动到下一个视角
            currentDeviceViewIndex++;

            // 离开第一个位置（开关位置）时，如果设备控制器存在且开关已打开，则自动关闭开关
            if (currentDeviceViewIndex == 1 && deviceController != null && !deviceController.isSwitchOn)
            {
                Debug.Log("[CameraController] 漫游开始：自动打开设备开关");
                deviceController.ToggleSwitch();
            }
            
        }
        
        isTouringDeviceViews = false;
    }
    
    /// <summary>
    /// 平滑过渡到指定位置的协程
    /// </summary>
    private IEnumerator TransitionToPosition(Transform targetTransform)
    {
        if (targetTransform == null)
            yield break;
            
        isTransitioning = true;
        
        Vector3 startPosition = transform.position;
        Quaternion startRotation = transform.rotation;
        float startFov = mainCamera.fieldOfView;
        
        float elapsedTime = 0f;
        float transitionDuration = 1.0f; // 过渡时间，单位：秒
        
        while (elapsedTime < transitionDuration)
        {
            // 计算插值因子
            float t = elapsedTime / transitionDuration;
            
            // 平滑插值位置、旋转和视野
            transform.position = Vector3.Lerp(startPosition, targetTransform.position, t * positionTransitionSpeed);
            transform.rotation = Quaternion.Slerp(startRotation, targetTransform.rotation, t * rotationTransitionSpeed);
            
            // 如果目标Transform有Camera组件，则同步其视野
            Camera targetCamera = targetTransform.GetComponent<Camera>();
            if (targetCamera != null)
            {
                mainCamera.fieldOfView = Mathf.Lerp(startFov, targetCamera.fieldOfView, t * fovTransitionSpeed);
            }
            
            elapsedTime += Time.deltaTime;
            yield return null;
        }
        
        // 确保最终位置精确匹配
        transform.position = targetTransform.position;
        transform.rotation = targetTransform.rotation;
        
        // 如果目标Transform有Camera组件，则同步其视野
        Camera finalTargetCamera = targetTransform.GetComponent<Camera>();
        if (finalTargetCamera != null)
        {
            mainCamera.fieldOfView = finalTargetCamera.fieldOfView;
        }
        
        // 更新当前垂直角度
        currentVerticalAngle = transform.eulerAngles.x;
        if (currentVerticalAngle > 180)
        {
            currentVerticalAngle -= 360;
        }
        
        isTransitioning = false;
    }
    
#if UNITY_EDITOR
    /// <summary>
    /// 在编辑器中绘制辅助线，显示相机位置和视角
    /// </summary>
    private void OnDrawGizmos()
    {
        // 绘制总览初始位置
        if (firstCameraInitialPosition != null)
        {
            Gizmos.color = Color.blue;
            Gizmos.DrawSphere(firstCameraInitialPosition.position, 0.5f);
            Gizmos.DrawLine(firstCameraInitialPosition.position, 
                           firstCameraInitialPosition.position + firstCameraInitialPosition.forward * 2);
        }
        
        // 绘制设备视角位置
        if (deviceCameraPositions != null)
        {
            Gizmos.color = Color.green;
            foreach (Transform viewTransform in deviceCameraPositions)
            {
                if (viewTransform != null)
                {
                    Gizmos.DrawSphere(viewTransform.position, 0.3f);
                    Gizmos.DrawLine(viewTransform.position, 
                                   viewTransform.position + viewTransform.forward * 1.5f);
                }
            }
        }
    }
#endif
}