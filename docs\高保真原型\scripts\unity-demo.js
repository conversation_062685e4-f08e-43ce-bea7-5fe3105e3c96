/**
 * Unity演示页面专用脚本
 * 白云电气设备数字孪生系统Unity集成演示
 */

class UnityDemoSystem {
    constructor() {
        this.unityIntegration = null;
        this.isUnityLoaded = false;
        this.fpsCounter = 0;
        this.lastFpsUpdate = Date.now();
        
        this.init();
    }

    /**
     * 初始化演示系统
     */
    init() {
        console.log('Unity演示系统初始化');
        
        // 初始化时间显示
        this.initTimeDisplay();
        
        // 初始化Unity集成
        this.initUnityIntegration();
        
        // 绑定控制按钮事件
        this.bindControlEvents();
        
        // 启动FPS监控
        this.startFpsMonitoring();
        
        // 监听Unity加载完成事件
        this.listenForUnityEvents();
    }

    /**
     * 初始化时间显示
     */
    initTimeDisplay() {
        const updateTime = () => {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            
            const currentTimeEl = document.getElementById('currentTime');
            const headerTimeEl = document.getElementById('headerTime');
            
            if (currentTimeEl) currentTimeEl.textContent = timeString;
            if (headerTimeEl) headerTimeEl.textContent = timeString;
        };
        
        updateTime();
        setInterval(updateTime, 1000);
    }

    /**
     * 初始化Unity集成
     */
    initUnityIntegration() {
        // Unity集成将在unity-integration.js中自动初始化
        // 这里只需要等待初始化完成
        setTimeout(() => {
            if (window.UnityIntegration) {
                this.unityIntegration = new window.UnityIntegration();
            }
        }, 100);
    }

    /**
     * 绑定控制按钮事件
     */
    bindControlEvents() {
        // 视角控制按钮
        this.bindButton('overviewBtn', () => this.handleOverviewClick());
        this.bindButton('tourBtn', () => this.handleTourClick());
        this.bindButton('expandBtn', () => this.handleExpandClick());
        this.bindButton('resetBtn', () => this.handleResetClick());
        
        // 场景控制按钮
        this.bindButton('fullscreenBtn', () => this.handleFullscreenClick());
        this.bindButton('screenshotBtn', () => this.handleScreenshotClick());
        this.bindButton('refreshBtn', () => this.handleRefreshClick());
        
        // 工具栏按钮
        this.bindButton('toolbarResetBtn', () => this.handleResetClick());
        this.bindButton('toolbarFullscreenBtn', () => this.handleFullscreenClick());
        this.bindButton('toolbarScreenshotBtn', () => this.handleScreenshotClick());
        this.bindButton('toolbarConsoleBtn', () => this.handleConsoleClick());
    }

    /**
     * 绑定按钮事件的辅助方法
     */
    bindButton(id, handler) {
        const button = document.getElementById(id);
        if (button) {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.setActiveButton(button);
                handler();
            });
        }
    }

    /**
     * 设置按钮激活状态
     */
    setActiveButton(activeButton) {
        // 移除其他按钮的激活状态
        document.querySelectorAll('.unity-control-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // 设置当前按钮为激活状态
        activeButton.classList.add('active');
        
        // 2秒后移除激活状态
        setTimeout(() => {
            activeButton.classList.remove('active');
        }, 2000);
    }

    /**
     * 处理总览视角点击
     */
    handleOverviewClick() {
        console.log('切换到总览视角');
        this.updateUnityStatus('切换视角中...');
        
        if (this.unityIntegration && this.unityIntegration.isUnityLoaded()) {
            this.unityIntegration.sendMessage("Main Camera", "SwitchToOverviewPosition");
        } else {
            this.showUnityNotLoadedMessage();
        }
    }

    /**
     * 处理自动漫游点击
     */
    handleTourClick() {
        console.log('开始自动漫游');
        this.updateUnityStatus('启动漫游模式...');
        
        if (this.unityIntegration && this.unityIntegration.isUnityLoaded()) {
            this.unityIntegration.sendMessage("Main Camera", "ToggleDeviceViewTour");
        } else {
            this.showUnityNotLoadedMessage();
        }
    }

    /**
     * 处理设备展开点击
     */
    handleExpandClick() {
        console.log('切换设备展开状态');
        this.updateUnityStatus('调整设备布局...');
        
        if (this.unityIntegration && this.unityIntegration.isUnityLoaded()) {
            this.unityIntegration.sendMessage("Device", "ToggleExpand");
        } else {
            this.showUnityNotLoadedMessage();
        }
    }

    /**
     * 处理重置视角点击
     */
    handleResetClick() {
        console.log('重置视角');
        this.updateUnityStatus('重置视角中...');
        
        if (this.unityIntegration && this.unityIntegration.isUnityLoaded()) {
            this.unityIntegration.sendMessage("Main Camera", "ResetView");
        } else {
            this.showUnityNotLoadedMessage();
        }
    }

    /**
     * 处理全屏显示点击
     */
    handleFullscreenClick() {
        console.log('切换全屏模式');
        const unityContainer = document.getElementById('unityContainer');
        
        if (unityContainer) {
            if (document.fullscreenElement) {
                document.exitFullscreen();
            } else {
                unityContainer.requestFullscreen().catch(err => {
                    console.error('无法进入全屏模式:', err);
                });
            }
        }
    }

    /**
     * 处理截图点击
     */
    handleScreenshotClick() {
        console.log('截取Unity场景');
        this.updateUnityStatus('正在截图...');
        
        if (this.unityIntegration && this.unityIntegration.isUnityLoaded()) {
            // 这里可以调用Unity的截图功能
            this.unityIntegration.sendMessage("ScreenshotManager", "TakeScreenshot");
            
            // 模拟截图完成
            setTimeout(() => {
                this.updateUnityStatus('截图已保存');
                setTimeout(() => this.updateUnityStatus('运行正常'), 2000);
            }, 1000);
        } else {
            this.showUnityNotLoadedMessage();
        }
    }

    /**
     * 处理刷新场景点击
     */
    handleRefreshClick() {
        console.log('刷新Unity场景');
        this.updateUnityStatus('刷新场景中...');
        
        if (this.unityIntegration) {
            // 重新加载Unity
            this.unityIntegration.dispose();
            setTimeout(() => {
                this.unityIntegration = new window.UnityIntegration();
            }, 1000);
        }
    }

    /**
     * 处理控制台点击
     */
    handleConsoleClick() {
        console.log('打开Unity控制台');
        // 这里可以显示Unity的调试信息
        alert('Unity控制台功能开发中...\n\n当前状态:\n- Unity版本: 2023.3.0f1\n- WebGL版本: 2.0\n- 加载状态: ' + (this.isUnityLoaded ? '已加载' : '未加载'));
    }

    /**
     * 更新Unity状态显示
     */
    updateUnityStatus(status) {
        const statusEl = document.getElementById('unityLoadStatus');
        if (statusEl) {
            statusEl.textContent = status;
        }
    }

    /**
     * 显示Unity未加载消息
     */
    showUnityNotLoadedMessage() {
        this.updateUnityStatus('Unity未加载');
        console.warn('Unity尚未加载完成，无法执行操作');
        
        // 显示提示消息
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 170, 0, 0.9);
            color: #1a1f2e;
            padding: 15px 20px;
            border-radius: 8px;
            font-weight: 600;
            z-index: 10000;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        `;
        notification.textContent = 'Unity正在加载中，请稍候...';
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    /**
     * 启动FPS监控
     */
    startFpsMonitoring() {
        const updateFps = () => {
            this.fpsCounter++;
            const now = Date.now();

            if (now - this.lastFpsUpdate >= 1000) {
                const fps = Math.round(this.fpsCounter * 1000 / (now - this.lastFpsUpdate));
                const fpsEl = document.getElementById('unityFPS');
                if (fpsEl) {
                    fpsEl.textContent = fps + ' FPS';
                }

                this.fpsCounter = 0;
                this.lastFpsUpdate = now;
            }

            requestAnimationFrame(updateFps);
        };

        updateFps();
    }

    /**
     * 监听Unity事件
     */
    listenForUnityEvents() {
        // 监听Unity加载完成事件
        document.addEventListener('unityLoaded', () => {
            this.onUnityLoaded();
        });

        // 监听设备选择事件
        document.addEventListener('deviceSelected', (event) => {
            this.onDeviceSelected(event.detail);
        });
    }

    /**
     * Unity加载完成处理
     */
    onUnityLoaded() {
        console.log('Unity演示系统：Unity加载完成');
        this.isUnityLoaded = true;

        // 更新状态指示器
        const statusIndicator = document.getElementById('unityStatusIndicator');
        if (statusIndicator) {
            statusIndicator.classList.add('loaded');
            statusIndicator.innerHTML = '<i class="fas fa-circle"></i><span>Unity状态: 已加载</span>';
        }

        // 更新加载状态
        this.updateUnityStatus('运行正常');
    }

    /**
     * 设备选择处理
     */
    onDeviceSelected(device) {
        console.log('设备被选择:', device);
        // 这里可以更新右侧面板显示选中设备的详细信息
    }
}

// 页面加载完成后初始化演示系统
document.addEventListener('DOMContentLoaded', () => {
    window.unityDemoSystem = new UnityDemoSystem();
});

// 导出演示系统类
window.UnityDemoSystem = UnityDemoSystem;
                
                this.fpsCounter = 0;
                this.lastFpsUpdate = now;
            }
            
            requestAnimationFrame(updateFps);
        };
        
        updateFps();
    }

    /**
     * 监听Unity事件
     */
    listenForUnityEvents() {
        // 监听Unity加载完成事件
        document.addEventListener('unityLoaded', () => {
            this.onUnityLoaded();
        });
        
        // 监听设备选择事件
        document.addEventListener('deviceSelected', (event) => {
            this.onDeviceSelected(event.detail);
        });
    }

    /**
     * Unity加载完成处理
     */
    onUnityLoaded() {
        console.log('Unity演示系统：Unity加载完成');
        this.isUnityLoaded = true;
        
        // 更新状态指示器
        const statusIndicator = document.getElementById('unityStatusIndicator');
        if (statusIndicator) {
            statusIndicator.classList.add('loaded');
            statusIndicator.innerHTML = '<i class="fas fa-circle"></i><span>Unity状态: 已加载</span>';
        }
        
        // 更新加载状态
        this.updateUnityStatus('运行正常');
    }

    /**
     * 设备选择处理
     */
    onDeviceSelected(device) {
        console.log('设备被选择:', device);
        // 这里可以更新右侧面板显示选中设备的详细信息
    }
}

// 页面加载完成后初始化演示系统
document.addEventListener('DOMContentLoaded', () => {
    window.unityDemoSystem = new UnityDemoSystem();
});

// 导出演示系统类
window.UnityDemoSystem = UnityDemoSystem;
