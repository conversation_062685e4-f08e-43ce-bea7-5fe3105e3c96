/**
 * Unity WebGL集成模块
 * 白云电气设备数字孪生系统高保真原型
 */

class UnityIntegration {
    constructor() {
        this.unityInstance = null;
        this.isLoaded = false;
        this.loadingProgress = 0;
        this.unityIframe = null;
        this.config = {
            // Unity WebGL路径配置
            unityPath: "../../webgl/index.html", // 相对于高保真原型的Unity WebGL路径
            companyName: "BaiYunGroup",
            productName: "DigitalTwin",
            productVersion: "1.0.0"
        };

        this.init();
    }

    /**
     * 初始化Unity集成
     */
    init() {
        console.log('Unity集成模块初始化');
        this.setupUnityContainer();
        this.loadRealUnity(); // 加载真实的Unity WebGL
    }

    /**
     * 设置Unity容器
     */
    setupUnityContainer() {
        const container = document.getElementById('unityContainer');
        if (!container) {
            console.error('Unity容器未找到');
            return;
        }

        // 设置容器样式
        container.style.position = 'relative';
        container.style.width = '100%';
        container.style.height = '100%';
        container.style.overflow = 'hidden';
    }

    /**
     * 加载真实的Unity WebGL
     */
    loadRealUnity() {
        const progressBar = document.getElementById('loadingProgress');
        const placeholder = document.querySelector('.unity-placeholder');

        if (!progressBar || !placeholder) return;

        // 更新加载文本
        this.updateLoadingText(0);

        // 创建Unity iframe
        this.createUnityIframe();

        // 监听Unity加载完成消息
        this.setupMessageListener();

        // 模拟加载进度（因为无法直接获取Unity加载进度）
        this.simulateLoadingProgress();
    }

    /**
     * 创建Unity iframe
     */
    createUnityIframe() {
        const container = document.getElementById('unityContainer');
        if (!container) return;

        this.unityIframe = document.createElement('iframe');
        this.unityIframe.src = this.config.unityPath;
        this.unityIframe.style.cssText = `
            width: 100%;
            height: 100%;
            border: none;
            background: transparent;
            display: none;
        `;
        this.unityIframe.allow = "autoplay; fullscreen; microphone; camera";

        container.appendChild(this.unityIframe);
    }

    /**
     * 设置消息监听器
     */
    setupMessageListener() {
        window.addEventListener('message', (event) => {
            if (event.data.type === 'unityLoaded') {
                console.log('收到Unity加载完成消息');
                this.onUnityReallyLoaded();
            }
        });
    }

    /**
     * 模拟加载进度
     */
    simulateLoadingProgress() {
        const progressBar = document.getElementById('loadingProgress');
        if (!progressBar) return;

        let progress = 0;
        const loadingInterval = setInterval(() => {
            progress += Math.random() * 10 + 5;

            if (progress >= 95) {
                progress = 95; // 保持在95%，等待真实Unity加载完成
                clearInterval(loadingInterval);
            }

            progressBar.style.width = progress + '%';
            this.loadingProgress = progress;

            // 更新加载文本
            this.updateLoadingText(progress);

        }, 300);
    }

    /**
     * 更新加载文本
     */
    updateLoadingText(progress) {
        const placeholder = document.querySelector('.placeholder-content h3');
        if (!placeholder) return;

        if (progress < 30) {
            placeholder.textContent = '正在初始化Unity引擎...';
        } else if (progress < 60) {
            placeholder.textContent = '正在加载3D模型资源...';
        } else if (progress < 90) {
            placeholder.textContent = '正在配置设备参数...';
        } else if (progress < 95) {
            placeholder.textContent = '正在建立连接...';
        } else {
            placeholder.textContent = '即将完成加载...';
        }
    }

    /**
     * Unity真正加载完成处理
     */
    onUnityReallyLoaded() {
        console.log('Unity WebGL真实加载完成');

        // 完成进度条
        const progressBar = document.getElementById('loadingProgress');
        if (progressBar) {
            progressBar.style.width = '100%';
        }

        // 更新加载文本
        this.updateLoadingText(100);

        // 延迟显示Unity内容
        setTimeout(() => {
            this.showRealUnityContent();
        }, 1000);
    }

    /**
     * 显示真实Unity内容
     */
    showRealUnityContent() {
        // 隐藏加载占位符
        const placeholder = document.querySelector('.unity-placeholder');
        if (placeholder) {
            placeholder.style.opacity = '0';
            placeholder.style.transition = 'opacity 0.5s ease-out';

            setTimeout(() => {
                placeholder.style.display = 'none';

                // 显示Unity iframe
                if (this.unityIframe) {
                    this.unityIframe.style.display = 'block';
                    this.unityIframe.style.opacity = '0';
                    this.unityIframe.style.transition = 'opacity 0.5s ease-in';

                    setTimeout(() => {
                        this.unityIframe.style.opacity = '1';
                    }, 100);
                }
            }, 500);
        }

        this.isLoaded = true;

        // 通知主系统Unity已加载
        if (window.digitalTwinSystem) {
            window.digitalTwinSystem.onUnityLoaded(this);
        }
    }

    /**
     * 显示Unity内容（在原型中显示模拟内容）
     */
    showUnityContent() {
        const container = document.getElementById('unityContainer');
        if (!container) return;

        // 创建模拟的3D场景内容
        const mockUnityContent = this.createMockUnityContent();
        container.appendChild(mockUnityContent);
    }

    /**
     * 创建模拟的Unity内容
     */
    createMockUnityContent() {
        const mockContent = document.createElement('div');
        mockContent.className = 'mock-unity-content';
        mockContent.style.cssText = `
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, 
                rgba(10, 26, 42, 0.9) 0%, 
                rgba(26, 31, 46, 0.9) 50%, 
                rgba(42, 49, 66, 0.9) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        `;

        // 添加3D模型占位符
        const modelPlaceholder = document.createElement('div');
        modelPlaceholder.className = 'model-placeholder';
        modelPlaceholder.style.cssText = `
            width: 300px;
            height: 200px;
            background: linear-gradient(45deg, 
                rgba(0, 212, 255, 0.2) 0%, 
                rgba(0, 153, 204, 0.2) 100%);
            border: 2px solid rgba(0, 212, 255, 0.5);
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #00d4ff;
            font-size: 16px;
            text-align: center;
            backdrop-filter: blur(5px);
            animation: modelRotate 4s ease-in-out infinite alternate;
        `;

        modelPlaceholder.innerHTML = `
            <i class="fas fa-cube" style="font-size: 48px; margin-bottom: 15px; animation: rotating 3s linear infinite;"></i>
            <div style="font-weight: 600;">白云电气设备模型</div>
            <div style="font-size: 12px; margin-top: 5px; opacity: 0.8;">3D数字孪生展示</div>
        `;

        mockContent.appendChild(modelPlaceholder);

        // 添加控制提示
        const controlHints = document.createElement('div');
        controlHints.className = 'control-hints';
        controlHints.style.cssText = `
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: rgba(184, 197, 214, 0.8);
            font-size: 12px;
            line-height: 1.5;
        `;

        controlHints.innerHTML = `
            <div><i class="fas fa-mouse"></i> 鼠标拖拽：旋转视角</div>
            <div><i class="fas fa-scroll"></i> 滚轮：缩放视图</div>
            <div><i class="fas fa-hand-pointer"></i> 点击设备：查看详情</div>
        `;

        mockContent.appendChild(controlHints);

        // 添加设备状态指示器
        const statusIndicators = this.createStatusIndicators();
        mockContent.appendChild(statusIndicators);

        return mockContent;
    }

    /**
     * 创建设备状态指示器
     */
    createStatusIndicators() {
        const indicators = document.createElement('div');
        indicators.className = 'device-indicators';
        indicators.style.cssText = `
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        `;

        const devices = [
            { name: '主变压器', status: 'running', x: 80, y: 60 },
            { name: '开关柜A', status: 'running', x: 40, y: 80 },
            { name: '开关柜B', status: 'warning', x: 60, y: 40 },
            { name: '保护装置', status: 'running', x: 20, y: 70 }
        ];

        devices.forEach(device => {
            const indicator = document.createElement('div');
            indicator.className = 'device-indicator';
            indicator.style.cssText = `
                position: absolute;
                left: ${device.x}%;
                top: ${device.y}%;
                width: 12px;
                height: 12px;
                border-radius: 50%;
                border: 2px solid rgba(255, 255, 255, 0.8);
                cursor: pointer;
                transition: all 0.3s ease;
                z-index: 10;
            `;

            // 设置状态颜色
            const statusColors = {
                running: '#00ff88',
                warning: '#ffaa00',
                error: '#ff4444'
            };

            indicator.style.backgroundColor = statusColors[device.status];
            indicator.style.boxShadow = `0 0 10px ${statusColors[device.status]}`;

            // 添加脉冲动画
            if (device.status === 'running') {
                indicator.style.animation = 'pulse 2s ease-in-out infinite';
            } else if (device.status === 'warning') {
                indicator.style.animation = 'warningBlink 1s ease-in-out infinite';
            }

            // 添加悬停效果
            indicator.addEventListener('mouseenter', () => {
                indicator.style.transform = 'scale(1.5)';
                this.showDeviceTooltip(device, indicator);
            });

            indicator.addEventListener('mouseleave', () => {
                indicator.style.transform = 'scale(1)';
                this.hideDeviceTooltip();
            });

            // 添加点击事件
            indicator.addEventListener('click', (event) => {
                this.selectDevice(device, event);
            });

            indicators.appendChild(indicator);
        });

        return indicators;
    }

    /**
     * 显示设备工具提示
     */
    showDeviceTooltip(device, element) {
        const tooltip = document.createElement('div');
        tooltip.className = 'device-tooltip';
        tooltip.style.cssText = `
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(26, 31, 46, 0.95);
            color: #ffffff;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            border: 1px solid rgba(0, 212, 255, 0.3);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            margin-bottom: 5px;
        `;

        const statusText = {
            running: '运行正常',
            warning: '运行异常',
            error: '设备故障'
        };

        tooltip.innerHTML = `
            <div style="font-weight: 600;">${device.name}</div>
            <div style="color: #b8c5d6; margin-top: 2px;">${statusText[device.status]}</div>
        `;

        element.appendChild(tooltip);
    }

    /**
     * 隐藏设备工具提示
     */
    hideDeviceTooltip() {
        const tooltips = document.querySelectorAll('.device-tooltip');
        tooltips.forEach(tooltip => tooltip.remove());
    }

    /**
     * 选择设备
     */
    selectDevice(device, event) {
        console.log(`选择设备: ${device.name}`);

        // 模拟设备选择效果
        const indicators = document.querySelectorAll('.device-indicator');
        indicators.forEach(indicator => {
            indicator.style.border = '2px solid rgba(255, 255, 255, 0.8)';
        });

        // 高亮选中的设备
        if (event && event.target) {
            event.target.style.border = '2px solid #00d4ff';
            event.target.style.boxShadow = '0 0 20px #00d4ff';
        }

        // 触发设备详情显示
        this.showDeviceDetails(device);
    }

    /**
     * 显示设备详情
     */
    showDeviceDetails(device) {
        // 这里可以触发右侧面板显示设备详细信息
        console.log(`显示${device.name}详细信息`);
        
        // 可以与主系统交互，更新右侧面板内容
        if (window.digitalTwinSystem) {
            // 触发设备详情更新
            const event = new CustomEvent('deviceSelected', {
                detail: device
            });
            document.dispatchEvent(event);
        }
    }

    /**
     * Unity消息发送（真实Unity通信）
     */
    sendMessage(gameObject, methodName, parameter = '') {
        console.log(`发送Unity消息: ${gameObject}.${methodName}(${parameter})`);

        if (this.isLoaded && this.unityIframe) {
            // 向Unity iframe发送消息
            this.unityIframe.contentWindow.postMessage({
                type: 'unityCommand',
                target: gameObject,
                method: methodName,
                parameter: parameter
            }, '*');
        } else {
            console.warn('Unity尚未加载完成，无法发送消息');
            // 如果Unity未加载，可以选择模拟响应或排队等待
            this.simulateUnityResponse(methodName);
        }
    }

    /**
     * 模拟Unity响应（当Unity未加载时的备用方案）
     */
    simulateUnityResponse(methodName) {
        switch (methodName) {
            case 'SwitchToOverviewPosition':
                this.simulateViewChange('overview');
                break;
            case 'ToggleDeviceViewTour':
                this.simulateViewChange('tour');
                break;
            case 'ToggleExpand':
                this.simulateDeviceExpand();
                break;
            case 'ResetView':
                this.simulateViewChange('reset');
                break;
        }
    }

    /**
     * 模拟视角变化
     */
    simulateViewChange(viewType) {
        const modelPlaceholder = document.querySelector('.model-placeholder');
        if (!modelPlaceholder) return;

        // 添加视角变化动画
        modelPlaceholder.style.transition = 'transform 1s ease-in-out';
        
        switch (viewType) {
            case 'overview':
                modelPlaceholder.style.transform = 'scale(0.8) rotateY(0deg)';
                break;
            case 'tour':
                modelPlaceholder.style.animation = 'modelRotate 2s ease-in-out infinite';
                break;
            case 'reset':
                modelPlaceholder.style.transform = 'scale(1) rotateY(0deg)';
                modelPlaceholder.style.animation = 'none';
                break;
        }
    }

    /**
     * 模拟设备展开
     */
    simulateDeviceExpand() {
        const indicators = document.querySelectorAll('.device-indicator');
        const isExpanded = indicators[0].style.transform.includes('scale(1.2)');
        
        indicators.forEach((indicator, index) => {
            if (isExpanded) {
                indicator.style.transform = 'scale(1)';
                indicator.style.left = `${[80, 40, 60, 20][index]}%`;
                indicator.style.top = `${[60, 80, 40, 70][index]}%`;
            } else {
                indicator.style.transform = 'scale(1.2)';
                indicator.style.left = `${[85, 35, 65, 15][index]}%`;
                indicator.style.top = `${[55, 85, 35, 75][index]}%`;
            }
        });
    }

    /**
     * 获取Unity实例
     */
    getInstance() {
        return this.unityInstance;
    }

    /**
     * 检查是否已加载
     */
    isUnityLoaded() {
        return this.isLoaded;
    }

    /**
     * 销毁Unity实例
     */
    dispose() {
        if (this.unityIframe) {
            // 移除iframe
            this.unityIframe.remove();
            this.unityIframe = null;
        }

        if (this.unityInstance && this.unityInstance.Quit) {
            this.unityInstance.Quit();
        }

        this.unityInstance = null;
        this.isLoaded = false;
    }
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes modelRotate {
        0% { transform: rotateY(0deg) scale(1); }
        50% { transform: rotateY(180deg) scale(1.1); }
        100% { transform: rotateY(360deg) scale(1); }
    }
`;
document.head.appendChild(style);

// 扩展DigitalTwinSystem类以集成Unity功能
if (window.DigitalTwinSystem) {
    const originalInit = window.DigitalTwinSystem.prototype.init;
    
    window.DigitalTwinSystem.prototype.init = function() {
        originalInit.call(this);
        this.unityIntegration = new UnityIntegration();
    };

    // 重写Unity相关方法
    window.DigitalTwinSystem.prototype.onUnityLoaded = function(unityIntegration) {
        this.isUnityLoaded = true;
        this.unityInstance = unityIntegration;
        console.log('Unity WebGL集成完成');
    };

    // 重写消息发送方法
    const originalHandleOverviewClick = window.DigitalTwinSystem.prototype.handleOverviewClick;
    const originalHandleTourClick = window.DigitalTwinSystem.prototype.handleTourClick;
    const originalHandleExpandClick = window.DigitalTwinSystem.prototype.handleExpandClick;

    window.DigitalTwinSystem.prototype.handleOverviewClick = function() {
        originalHandleOverviewClick.call(this);
        if (this.unityIntegration) {
            this.unityIntegration.sendMessage("Main Camera", "SwitchToOverviewPosition");
        }
    };

    window.DigitalTwinSystem.prototype.handleTourClick = function() {
        originalHandleTourClick.call(this);
        if (this.unityIntegration) {
            this.unityIntegration.sendMessage("Main Camera", "ToggleDeviceViewTour");
        }
    };

    window.DigitalTwinSystem.prototype.handleExpandClick = function() {
        originalHandleExpandClick.call(this);
        if (this.unityIntegration) {
            this.unityIntegration.sendMessage("Device", "ToggleExpand");
        }
    };
}

// 导出Unity集成类
window.UnityIntegration = UnityIntegration;
