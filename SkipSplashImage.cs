using UnityEngine;
using UnityEngine.Rendering;

static class SkipSplashImage
{
    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSplashScreen)]
    private static void BeforeSplashScreen()
    {
        #if UNITY_WEBGL
        Application.ExternalEval(@"
            if (typeof WebAssembly !== 'undefined' && !WebAssembly.instantiateStreaming) {
                WebAssembly.instantiateStreaming = async (response, importObject) => {
                    const bytes = await response.arrayBuffer();
                    return WebAssembly.instantiate(bytes, importObject);
                };
            }
        ");
        #endif
        
        SplashScreen.Stop(SplashScreen.StopBehavior.StopImmediate);
    }
}
