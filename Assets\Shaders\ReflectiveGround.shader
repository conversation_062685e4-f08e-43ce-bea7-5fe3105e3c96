Shader "Custom/ReflectiveGround"
{
    Properties
    {
        _Color ("Color", Color) = (1,1,1,0.5)
        _MainTex ("Albedo (RGB)", 2D) = "white" {}
        _Glossiness ("Smoothness", Range(0,1)) = 0.95  // 增加默认光滑度
        _Metallic ("Metallic", Range(0,1)) = 0.8  // 增加默认金属度
        _ReflectionTex ("Reflection", 2D) = "white" {}
        _ReflectionStrength ("Reflection Strength", Range(0,2)) = 1.5  // 默认较高的反射强度
        _FresnelPower ("Fresnel Power", Range(0,10)) = 1.0  // 降低菲涅尔效果，使整体反射更均匀
        _ReflectionBlur ("Reflection Blur", Range(0,0.1)) = 0.0  // 保持清晰的反射
    }
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        LOD 200

        CGPROGRAM
        #pragma surface surf Standard fullforwardshadows
        #pragma target 3.0

        sampler2D _MainTex;
        sampler2D _ReflectionTex;

        struct Input
        {
            float2 uv_MainTex;
            float4 screenPos;
            float3 worldNormal;
            float3 viewDir;
        };

        half _Glossiness;
        half _Metallic;
        fixed4 _Color;
        half _ReflectionStrength;
        half _FresnelPower;
        half _ReflectionBlur;

        void surf (Input IN, inout SurfaceOutputStandard o)
        {
            // 基础纹理
            fixed4 c = tex2D (_MainTex, IN.uv_MainTex) * _Color;
            
            // 反射纹理
            float2 screenUV = IN.screenPos.xy / IN.screenPos.w;
            
            // 添加简单的模糊效果
            fixed4 refl = fixed4(0,0,0,0);
            if (_ReflectionBlur > 0) {
                float blurSize = _ReflectionBlur * 0.01;
                for(int x = -2; x <= 2; x++) {
                    for(int y = -2; y <= 2; y++) {
                        float2 offset = float2(x, y) * blurSize;
                        refl += tex2D(_ReflectionTex, screenUV + offset);
                    }
                }
                refl /= 25.0;
            } else {
                refl = tex2D(_ReflectionTex, screenUV);
            }
            
            // 计算菲涅尔效果（视角反射）
            float fresnel = pow(1.0 - saturate(dot(normalize(IN.viewDir), normalize(IN.worldNormal))), _FresnelPower);
            
            // 混合基础颜色和反射，应用菲涅尔效果，但保持较高的基础反射
            float finalReflectionStrength = max(_ReflectionStrength * 0.8, _ReflectionStrength * fresnel);
            o.Albedo = lerp(c.rgb, refl.rgb, finalReflectionStrength);
            o.Metallic = _Metallic;
            o.Smoothness = _Glossiness;
            o.Alpha = c.a;
        }
        ENDCG
    }
    FallBack "Diffuse"
}