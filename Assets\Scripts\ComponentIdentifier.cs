using UnityEngine;

/// <summary>
/// 组件标识器 - 用于在编辑器中标识设备的各个组件
/// 这个脚本不包含任何运行时功能，仅用于在编辑器中更容易识别和选择组件
/// </summary>
[AddComponentMenu("设备交互/组件标识器")]
public class ComponentIdentifier : MonoBehaviour
{
    public enum ComponentType
    {
        DeviceLogo,       // 设备Logo
        Switch,           // 开关
        Door,             // 门
        InletPipe,        // 进水管
        OutletPipe,       // 出水管
        StatusDisplay,    // 状态显示器
        ExpandableComponent, // 可展开组件
        FunctionalArea    // 功能区域
    }
    
    [Tooltip("组件类型")]
    public ComponentType componentType;
    
    [Tooltip("组件描述")]
    [TextArea(2, 5)]
    public string description;
    
    // 在编辑器中显示图标
    private void OnDrawGizmos()
    {
        // 根据组件类型显示不同的图标和颜色
        switch (componentType)
        {
            case ComponentType.DeviceLogo:
                Gizmos.color = Color.cyan;
                Gizmos.DrawSphere(transform.position, 0.05f);
                break;
                
            case ComponentType.Switch:
                Gizmos.color = Color.yellow;
                Gizmos.DrawCube(transform.position, new Vector3(0.05f, 0.05f, 0.05f));
                break;
                
            case ComponentType.Door:
                Gizmos.color = Color.magenta;
                Gizmos.DrawWireCube(transform.position, new Vector3(0.1f, 0.1f, 0.02f));
                break;
                
            case ComponentType.InletPipe:
            case ComponentType.OutletPipe:
                Gizmos.color = Color.blue;
                Gizmos.DrawWireSphere(transform.position, 0.03f);
                // 绘制水流方向
                Gizmos.DrawRay(transform.position, transform.forward * 0.1f);
                break;
                
            case ComponentType.StatusDisplay:
                Gizmos.color = Color.green;
                Gizmos.DrawWireSphere(transform.position, 0.02f);
                break;
                
            case ComponentType.ExpandableComponent:
                Gizmos.color = Color.red;
                Gizmos.DrawLine(transform.position, transform.parent.position);
                Gizmos.DrawWireSphere(transform.position, 0.02f);
                break;
                
            case ComponentType.FunctionalArea:
                Gizmos.color = new Color(1f, 0.5f, 0f, 0.3f); // 橙色半透明
                Gizmos.DrawWireCube(transform.position, transform.localScale);
                break;
        }
    }
}