mergeInto(LibraryManager.library, {
    // 隐藏echarts图表
    HideEchartsInternal: function() {
        console.log("[JS] 隐藏echarts图表");
        try {
            // 获取所有echarts容器
            var chartContainers = document.querySelectorAll('.chart-container');
            
            // 隐藏所有echarts容器
            for (var i = 0; i < chartContainers.length; i++) {
                chartContainers[i].style.display = 'none';
            }
            
            // 也可以单独处理特定的图表
            var chart1 = document.getElementById('chart1');
            var chart2 = document.getElementById('chart2');
            var chart3 = document.getElementById('chart3');
            var chart4 = document.getElementById('chart4');
            
            if (chart1) chart1.style.display = 'none';
            if (chart2) chart2.style.display = 'none';
            if (chart3) chart3.style.display = 'none';
            if (chart4) chart4.style.display = 'none';
            
            console.log("[JS] 已隐藏" + chartContainers.length + "个echarts图表");
        } catch (e) {
            console.error("[JS] 隐藏echarts图表时出错:", e);
        }
    },
    
    // 显示echarts图表
    ShowEchartsInternal: function() {
        console.log("[JS] 显示echarts图表");
        try {
            // 获取所有echarts容器
            var chartContainers = document.querySelectorAll('.chart-container');
            
            // 显示所有echarts容器
            for (var i = 0; i < chartContainers.length; i++) {
                chartContainers[i].style.display = 'block';
            }
            
            // 也可以单独处理特定的图表
            var chart1 = document.getElementById('chart1');
            var chart2 = document.getElementById('chart2');
            var chart3 = document.getElementById('chart3');
            var chart4 = document.getElementById('chart4');
            
            if (chart1) chart1.style.display = 'block';
            if (chart2) chart2.style.display = 'block';
            if (chart3) chart3.style.display = 'block';
            if (chart4) chart4.style.display = 'block';
            
            // 重新调整echarts大小以适应显示
            if (window.echarts) {
                try {
                    // 如果存在全局echarts实例，重新调整大小
                    var charts = [];
                    // 验证并添加有效的图表实例
                    var instances = [
                        { name: 'chart1Instance', elem: 'chart1' },
                        { name: 'chart2Instance', elem: 'chart2' },
                        { name: 'chart3Instance', elem: 'chart3' },
                        { name: 'chart4Instance', elem: 'chart4' }
                    ];
                    
                    instances.forEach(function(instance) {
                        var chartInstance = window[instance.name];
                        var chartElement = document.getElementById(instance.elem);
                        if (chartInstance && chartElement && 
                            chartElement.style.display !== 'none' && 
                            typeof chartInstance.resize === 'function') {
                            charts.push(chartInstance);
                        }
                    });
                    
                    // 对所有有效的图表实例调用resize方法
                    charts.forEach(function(chart) {
                        chart.resize();
                    });
                } catch (resizeError) {
                    console.error('[JS] 调整图表大小时出错:', resizeError);
                }
                
                // 触发一次resize事件
                if (typeof window !== 'undefined' && typeof window.dispatchEvent === 'function' && typeof Event === 'function') {
                    try {
                        //window.dispatchEvent(new Event('resize'));
                    } catch (e) {
                        console.error('[JS] 触发resize事件时出错:', e);
                    }
                } else {
                    console.warn('[JS] 无法触发resize事件: window对象、dispatchEvent方法或Event构造函数不可用');
                }
            }
            
            console.log("[JS] 已显示" + chartContainers.length + "个echarts图表");
        } catch (e) {
            console.error("[JS] 显示echarts图表时出错:", e);
        }
    }
});