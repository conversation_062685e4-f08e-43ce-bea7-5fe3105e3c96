/**
 * MQTT 电气系统集成测试脚本
 * 用于验证 main.html 中的 MQTT 实时数据集成功能
 */

// 测试配置
const TEST_CONFIG = {
    mqttServer: 'wss://mqtt.qizhiyun.cc/mqtt',
    topic: '/189/D19QBHKRZ791U/ws/service',
    testDuration: 30000, // 30秒测试时间
    expectedParameters: [
        'HMI_30039_4', 'HMI_30039_5', 'HMI_30039_6', 'HMI_30039_9', 'HMI_30039_10',
        'HMI_32030', 'HMI_32032', 'HMI_32034',
        'HMI_32040', 'HMI_32042', 'HMI_32044', 'HMI_32046',
        'HMI_32048', 'HMI_32050'
    ]
};

/**
 * 集成测试类
 */
class MQTTIntegrationTest {
    constructor() {
        this.testResults = {
            connectionTest: null,
            dataReceptionTest: null,
            uiUpdateTest: null,
            errorHandlingTest: null,
            performanceTest: null
        };
        
        this.startTime = null;
        this.receivedDataCount = 0;
        this.uiUpdateCount = 0;
        this.errors = [];
    }

    /**
     * 运行完整的集成测试
     */
    async runFullTest() {
        console.log('=== 开始 MQTT 电气系统集成测试 ===');
        this.startTime = Date.now();
        
        try {
            // 1. 连接测试
            await this.testConnection();
            
            // 2. 数据接收测试
            await this.testDataReception();
            
            // 3. 界面更新测试
            await this.testUIUpdate();
            
            // 4. 错误处理测试
            await this.testErrorHandling();
            
            // 5. 性能测试
            await this.testPerformance();
            
            // 生成测试报告
            this.generateTestReport();
            
        } catch (error) {
            console.error('集成测试失败:', error);
            this.errors.push({ type: 'test_failure', error: error.message });
        }
        
        console.log('=== MQTT 电气系统集成测试完成 ===');
        return this.testResults;
    }

    /**
     * 测试 MQTT 连接
     */
    async testConnection() {
        console.log('1. 测试 MQTT 连接...');
        
        return new Promise((resolve) => {
            const timeout = setTimeout(() => {
                this.testResults.connectionTest = {
                    success: false,
                    error: '连接超时',
                    duration: 10000
                };
                resolve();
            }, 10000);
            
            // 检查连接状态
            const checkConnection = () => {
                const status = getMQTTConnectionStatus();
                if (status.isConnected) {
                    clearTimeout(timeout);
                    this.testResults.connectionTest = {
                        success: true,
                        duration: Date.now() - this.startTime,
                        details: status
                    };
                    console.log('✓ MQTT 连接测试通过');
                    resolve();
                } else {
                    setTimeout(checkConnection, 1000);
                }
            };
            
            checkConnection();
        });
    }

    /**
     * 测试数据接收
     */
    async testDataReception() {
        console.log('2. 测试数据接收...');
        
        return new Promise((resolve) => {
            const startTime = Date.now();
            const timeout = setTimeout(() => {
                this.testResults.dataReceptionTest = {
                    success: this.receivedDataCount > 0,
                    receivedCount: this.receivedDataCount,
                    duration: Date.now() - startTime,
                    expectedParameters: TEST_CONFIG.expectedParameters.length
                };
                
                if (this.receivedDataCount > 0) {
                    console.log(`✓ 数据接收测试通过 (接收到 ${this.receivedDataCount} 次数据)`);
                } else {
                    console.log('✗ 数据接收测试失败 (未接收到数据)');
                }
                
                resolve();
            }, 15000); // 15秒等待数据
            
            // 监听数据接收
            const originalCallback = mqttElectricalManager ? 
                mqttElectricalManager.addDataUpdateCallback : null;
            
            if (originalCallback) {
                mqttElectricalManager.addDataUpdateCallback((data, metadata) => {
                    this.receivedDataCount++;
                    
                    // 检查是否接收到足够的数据
                    if (this.receivedDataCount >= 3) {
                        clearTimeout(timeout);
                        this.testResults.dataReceptionTest = {
                            success: true,
                            receivedCount: this.receivedDataCount,
                            duration: Date.now() - startTime,
                            dataQuality: metadata ? metadata.dataQuality.score : 0
                        };
                        console.log(`✓ 数据接收测试通过 (接收到 ${this.receivedDataCount} 次数据)`);
                        resolve();
                    }
                });
            }
        });
    }

    /**
     * 测试界面更新
     */
    async testUIUpdate() {
        console.log('3. 测试界面更新...');
        
        const testElements = [
            'ready-status', 'running-status', 'fault-status', 'standby-status',
            'bus-voltage-uab-value', 'power-factor-value', 'load-reactive-power-value'
        ];
        
        let updatedElements = 0;
        const initialValues = {};
        
        // 记录初始值
        testElements.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                initialValues[elementId] = element.textContent;
            }
        });
        
        // 等待界面更新
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // 检查界面是否更新
        testElements.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element && element.textContent !== initialValues[elementId]) {
                updatedElements++;
            }
        });
        
        this.testResults.uiUpdateTest = {
            success: updatedElements > 0,
            updatedElements: updatedElements,
            totalElements: testElements.length,
            updateRate: (updatedElements / testElements.length) * 100
        };
        
        if (updatedElements > 0) {
            console.log(`✓ 界面更新测试通过 (${updatedElements}/${testElements.length} 个元素已更新)`);
        } else {
            console.log('✗ 界面更新测试失败 (没有元素被更新)');
        }
    }

    /**
     * 测试错误处理
     */
    async testErrorHandling() {
        console.log('4. 测试错误处理...');
        
        const errorsBefore = getErrorHistory().length;
        
        // 模拟一些错误情况
        try {
            // 测试无效数据处理
            if (mqttElectricalManager) {
                mqttElectricalManager.processElectricalData(null);
                mqttElectricalManager.processElectricalData({ invalid: 'data' });
            }
        } catch (error) {
            // 预期的错误
        }
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const errorsAfter = getErrorHistory().length;
        
        this.testResults.errorHandlingTest = {
            success: errorsAfter >= errorsBefore,
            errorsBefore: errorsBefore,
            errorsAfter: errorsAfter,
            newErrors: errorsAfter - errorsBefore
        };
        
        console.log(`✓ 错误处理测试通过 (记录了 ${errorsAfter - errorsBefore} 个新错误)`);
    }

    /**
     * 测试性能
     */
    async testPerformance() {
        console.log('5. 测试性能...');
        
        const debugInfo = getMQTTDebugInfo();
        const connectionStatus = getMQTTConnectionStatus();
        
        this.testResults.performanceTest = {
            success: true,
            connectionTime: this.testResults.connectionTest ? 
                this.testResults.connectionTest.duration : 0,
            dataProcessingStats: connectionStatus.dataProcessing,
            memoryUsage: performance.memory ? {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024)
            } : null
        };
        
        console.log('✓ 性能测试完成');
    }

    /**
     * 生成测试报告
     */
    generateTestReport() {
        console.log('\n=== 测试报告 ===');
        
        const totalTests = Object.keys(this.testResults).length;
        const passedTests = Object.values(this.testResults)
            .filter(result => result && result.success).length;
        
        console.log(`总测试数: ${totalTests}`);
        console.log(`通过测试: ${passedTests}`);
        console.log(`测试通过率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
        console.log(`总测试时间: ${((Date.now() - this.startTime) / 1000).toFixed(1)}秒`);
        
        // 详细结果
        Object.entries(this.testResults).forEach(([testName, result]) => {
            const status = result && result.success ? '✓' : '✗';
            console.log(`${status} ${testName}: ${result ? JSON.stringify(result, null, 2) : '未执行'}`);
        });
        
        // 建议
        console.log('\n=== 建议 ===');
        if (this.testResults.connectionTest && !this.testResults.connectionTest.success) {
            console.log('- 检查 MQTT 服务器连接配置');
        }
        if (this.testResults.dataReceptionTest && !this.testResults.dataReceptionTest.success) {
            console.log('- 检查 MQTT 主题订阅是否正确');
        }
        if (this.testResults.uiUpdateTest && !this.testResults.uiUpdateTest.success) {
            console.log('- 检查界面元素映射是否正确');
        }
        
        console.log('================\n');
    }
}

// 全局测试函数
window.runMQTTIntegrationTest = async function() {
    const test = new MQTTIntegrationTest();
    return await test.runFullTest();
};

// 快速连接测试
window.quickConnectionTest = function() {
    console.log('快速连接测试...');
    const status = getMQTTConnectionStatus();
    console.log('连接状态:', status);
    
    if (status.isConnected) {
        console.log('✓ MQTT 已连接');
        console.log('数据处理统计:', status.dataProcessing);
    } else {
        console.log('✗ MQTT 未连接');
        console.log('尝试重连...');
        reconnectMQTT();
    }
};

// 界面元素检查
window.checkUIElements = function() {
    console.log('检查界面元素...');
    
    const elements = [
        'ready-status', 'running-status', 'fault-status', 'standby-status', 'hv-wait-status',
        'bus-voltage-uab-value', 'bus-voltage-ubc-value', 'bus-voltage-uca-value',
        'svg-current-ia-value', 'svg-current-ib-value', 'svg-current-ic-value',
        'grid-reactive-current-value', 'load-reactive-power-value', 'power-factor-value'
    ];
    
    let foundElements = 0;
    elements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            foundElements++;
            console.log(`✓ ${elementId}: ${element.textContent}`);
        } else {
            console.log(`✗ ${elementId}: 未找到`);
        }
    });
    
    console.log(`界面元素检查完成: ${foundElements}/${elements.length} 个元素存在`);
};

console.log('MQTT 集成测试脚本已加载');
console.log('可用命令:');
console.log('- runMQTTIntegrationTest() - 运行完整集成测试');
console.log('- quickConnectionTest() - 快速连接测试');
console.log('- checkUIElements() - 检查界面元素');
