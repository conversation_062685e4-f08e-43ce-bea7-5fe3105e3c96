using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

/// <summary>
/// LineManager组件的自定义编辑器
/// </summary>
[CustomEditor(typeof(LineManager))]
public class LineManagerEditor : Editor
{
    private SerializedProperty connectionLinesProp;
    private SerializedProperty targetCanvasProp;
    private SerializedProperty mainCameraProp;
    private SerializedProperty updateFrequencyProp;
    
    private bool showConnectionSettings = true;
    private Dictionary<int, bool> connectionFoldouts = new Dictionary<int, bool>();
    
    private void OnEnable()
    {
        connectionLinesProp = serializedObject.FindProperty("connectionLines");
        targetCanvasProp = serializedObject.FindProperty("targetCanvas");
        mainCameraProp = serializedObject.FindProperty("mainCamera");
        updateFrequencyProp = serializedObject.FindProperty("updateFrequency");
    }
    
    public override void OnInspectorGUI()
    {
        serializedObject.Update();
        
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("连线管理器设置", EditorStyles.boldLabel);
        EditorGUILayout.Space();
        
        // 基本设置
        EditorGUILayout.PropertyField(targetCanvasProp, new GUIContent("目标Canvas", "连线所在的Canvas"));
        EditorGUILayout.PropertyField(mainCameraProp, new GUIContent("主相机", "用于世界坐标到屏幕坐标转换的相机"));
        
        // 更新频率设置
        EditorGUILayout.PropertyField(updateFrequencyProp, new GUIContent("更新频率", "每秒更新连线位置的次数"));
        
        EditorGUILayout.Space();
        
        // 连线设置
        showConnectionSettings = EditorGUILayout.Foldout(showConnectionSettings, "连线设置", true, EditorStyles.foldoutHeader);
        
        if (showConnectionSettings)
        {
            EditorGUI.indentLevel++;
            
            // 连线列表
            for (int i = 0; i < connectionLinesProp.arraySize; i++)
            {
                SerializedProperty lineProp = connectionLinesProp.GetArrayElementAtIndex(i);
                
                // 确保字典中有此索引
                if (!connectionFoldouts.ContainsKey(i))
                {
                    connectionFoldouts[i] = false;
                }
                
                EditorGUILayout.BeginHorizontal();
                
                // 获取连线名称
                SerializedProperty nameProp = lineProp.FindPropertyRelative("name");
                string lineName = nameProp.stringValue;
                if (string.IsNullOrEmpty(lineName))
                {
                    lineName = "连线 " + (i + 1);
                }
                
                // 显示折叠标题
                connectionFoldouts[i] = EditorGUILayout.Foldout(connectionFoldouts[i], lineName, true);
                
                // 删除按钮
                if (GUILayout.Button("删除", GUILayout.Width(60)))
                {
                    connectionLinesProp.DeleteArrayElementAtIndex(i);
                    serializedObject.ApplyModifiedProperties();
                    return;
                }
                
                EditorGUILayout.EndHorizontal();
                
                if (connectionFoldouts[i])
                {
                    EditorGUI.indentLevel++;
                    
                    // 连线属性
                    EditorGUILayout.PropertyField(nameProp, new GUIContent("连线名称", "连线的唯一名称"));
                    EditorGUILayout.PropertyField(lineProp.FindPropertyRelative("objectTransform"), new GUIContent("3D物体", "3D场景中的物体"));
                    EditorGUILayout.PropertyField(lineProp.FindPropertyRelative("uiElement"), new GUIContent("UI元素", "Canvas中的UI元素"));
                    EditorGUILayout.PropertyField(lineProp.FindPropertyRelative("lineObject"), new GUIContent("连线对象", "用于绘制连线的RectTransform"));
                    EditorGUILayout.PropertyField(lineProp.FindPropertyRelative("lineImage"), new GUIContent("连线图像", "连线的Image组件"));
                    EditorGUILayout.PropertyField(lineProp.FindPropertyRelative("lineWidth"), new GUIContent("连线宽度", "连线的宽度"));
                    EditorGUILayout.PropertyField(lineProp.FindPropertyRelative("lineColor"), new GUIContent("连线颜色", "连线的颜色"));
                    
                    EditorGUILayout.Space();
                    EditorGUILayout.LabelField("偏移设置", EditorStyles.boldLabel);
                    EditorGUILayout.PropertyField(lineProp.FindPropertyRelative("objectOffset"), new GUIContent("物体偏移", "3D物体连接点偏移"));
                    EditorGUILayout.PropertyField(lineProp.FindPropertyRelative("uiOffset"), new GUIContent("UI偏移", "UI元素连接点偏移"));
                    
                    EditorGUILayout.Space();
                    EditorGUILayout.LabelField("可见性设置", EditorStyles.boldLabel);
                    EditorGUILayout.PropertyField(lineProp.FindPropertyRelative("isVisible"), new GUIContent("是否可见", "连线是否显示"));
                    EditorGUILayout.PropertyField(lineProp.FindPropertyRelative("hideWhenOutOfView"), new GUIContent("视野外隐藏", "当3D物体不在相机视野内时是否隐藏连线"));
                    
                    EditorGUI.indentLevel--;
                    EditorGUILayout.Space();
                }
            }
            
            // 添加按钮
            EditorGUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace();
            if (GUILayout.Button("添加连线", GUILayout.Width(120)))
            {
                int newIndex = connectionLinesProp.arraySize;
                connectionLinesProp.arraySize++;
                
                // 初始化新连线
                SerializedProperty newLine = connectionLinesProp.GetArrayElementAtIndex(newIndex);
                newLine.FindPropertyRelative("name").stringValue = "新连线" + (newIndex + 1);
                newLine.FindPropertyRelative("lineColor").colorValue = Color.white;
                newLine.FindPropertyRelative("lineWidth").floatValue = 2f;
                newLine.FindPropertyRelative("isVisible").boolValue = true;
                newLine.FindPropertyRelative("hideWhenOutOfView").boolValue = true;
                
                // 展开新添加的连线
                connectionFoldouts[newIndex] = true;
            }
            GUILayout.FlexibleSpace();
            EditorGUILayout.EndHorizontal();
            
            EditorGUI.indentLevel--;
        }
        
        serializedObject.ApplyModifiedProperties();
        
        // 帮助信息
        EditorGUILayout.Space();
        EditorGUILayout.HelpBox("此组件用于管理3D物体与UI元素之间的连线。\n连线会自动更新位置，确保在相机旋转时保持正确连接。\n请确保每个连线都设置了有效的3D物体、UI元素和连线对象。", MessageType.Info);
        
        // 运行时提示
        if (Application.isPlaying)
        {
            EditorGUILayout.Space();
            EditorGUILayout.HelpBox("运行时修改的设置将在退出播放模式后丢失。", MessageType.Warning);
        }
    }
}