using UnityEngine;
using UnityEditor;
using UnityEngine.UI;

/// <summary>
/// 连线创建工具 - 提供快速创建连线预制体的功能
/// </summary>
public class LineCreationUtility : EditorWindow
{
    private Color lineColor = Color.white;
    private float lineWidth = 2f;
    private string prefabName = "LinePrefab";
    private string prefabPath = "Assets/Prefabs";
    
    [MenuItem("工具/创建连线预制体")]
    public static void ShowWindow()
    {
        GetWindow<LineCreationUtility>("连线预制体创建工具");
    }
    
    private void OnGUI()
    {
        EditorGUILayout.Space(10);
        EditorGUILayout.LabelField("连线预制体创建工具", EditorStyles.boldLabel);
        EditorGUILayout.Space(10);
        
        EditorGUILayout.LabelField("基本设置", EditorStyles.boldLabel);
        lineColor = EditorGUILayout.ColorField("连线颜色", lineColor);
        lineWidth = EditorGUILayout.FloatField("连线宽度", lineWidth);
        prefabName = EditorGUILayout.TextField("预制体名称", prefabName);
        
        EditorGUILayout.Space(5);
        EditorGUILayout.LabelField("保存设置", EditorStyles.boldLabel);
        prefabPath = EditorGUILayout.TextField("保存路径", prefabPath);
        
        EditorGUILayout.Space(10);
        EditorGUILayout.HelpBox("此工具将创建一个用于连线的UI Image预制体，并设置好基本属性。\n创建后的预制体可直接用于LineConnector组件。", MessageType.Info);
        
        EditorGUILayout.Space(10);
        if (GUILayout.Button("创建连线预制体", GUILayout.Height(30)))
        {
            CreateLinePrefab();
        }
    }
    
    private void CreateLinePrefab()
    {
        // 确保路径存在
        if (!System.IO.Directory.Exists(prefabPath))
        {
            System.IO.Directory.CreateDirectory(prefabPath);
            AssetDatabase.Refresh();
        }
        
        // 创建连线GameObject
        GameObject lineObject = new GameObject(prefabName);
        RectTransform rectTransform = lineObject.AddComponent<RectTransform>();
        Image image = lineObject.AddComponent<Image>();
        
        // 设置RectTransform属性
        rectTransform.anchorMin = new Vector2(0.5f, 0.5f);
        rectTransform.anchorMax = new Vector2(0.5f, 0.5f);
        rectTransform.pivot = new Vector2(0.5f, 0.5f);
        rectTransform.sizeDelta = new Vector2(100f, lineWidth);
        
        // 设置Image属性
        image.color = lineColor;
        
        // 创建预制体
        string fullPath = $"{prefabPath}/{prefabName}.prefab";
        
        // 确保文件名唯一
        string uniquePath = AssetDatabase.GenerateUniqueAssetPath(fullPath);
        
        // 创建预制体资源
        PrefabUtility.SaveAsPrefabAsset(lineObject, uniquePath);
        
        // 销毁临时对象
        DestroyImmediate(lineObject);
        
        // 显示成功消息
        EditorUtility.DisplayDialog("创建成功", $"连线预制体已创建并保存至: {uniquePath}", "确定");
        
        // 在Project窗口中选中并高亮显示新创建的预制体
        Object prefabAsset = AssetDatabase.LoadAssetAtPath(uniquePath, typeof(Object));
        Selection.activeObject = prefabAsset;
        EditorGUIUtility.PingObject(prefabAsset);
    }
}