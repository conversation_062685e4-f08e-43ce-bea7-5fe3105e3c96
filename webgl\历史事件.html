<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>桂林智源 SVG 数字化系统 - 历史事件</title>
    <link rel="shortcut icon" href="TemplateData/favicon.ico">
    <link rel="stylesheet" href="styles.css">
    <!-- 引入 echarts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /**
         * 历史事件页面专用样式
         * 基于项目统一的科技蓝色主题设计
         */
        .history-events-container {
            width: 1366px;
            height: 768px;
            margin: 0 auto;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            overflow: hidden;
            position: relative;
            font-family: var(--font-family);
        }

        /* 科技感背景动画 */
        .history-events-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(102, 224, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* 顶部标题栏 */
        .history-header {
            height: 80px;
            background: linear-gradient(90deg,
                rgba(26, 31, 46, 0.95) 0%,
                rgba(42, 49, 66, 0.95) 50%,
                rgba(26, 31, 46, 0.95) 100%);
            backdrop-filter: blur(10px);
            border-bottom: 2px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            box-shadow: var(--shadow-secondary);
            position: relative;
            z-index: 100;
        }

        .history-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg,
                transparent 0%,
                var(--primary-color) 25%,
                var(--accent-color) 50%,
                var(--primary-color) 75%,
                transparent 100%);
            background-size: 200% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        .history-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .history-title h1 {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }

        .history-title i {
            font-size: 32px;
            color: var(--primary-color);
            animation: logoSpin 4s linear infinite;
        }

        .history-time {
            font-family: var(--font-mono);
            font-size: 16px;
            font-weight: 600;
            color: var(--accent-color);
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.15) 0%,
                rgba(0, 153, 204, 0.15) 100%);
            padding: 10px 20px;
            border-radius: 20px;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 212, 255, 0.2);
        }

        /* 主内容区域 */
        .history-main {
            height: calc(768px - 80px);
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            padding: 20px;
        }

        /* 左侧筛选面板 */
        .filter-panel {
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-primary);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .filter-header {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.2) 0%,
                rgba(0, 153, 204, 0.2) 100%);
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .filter-header h3 {
            margin: 0;
            font-size: 18px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .filter-section {
            margin-bottom: 25px;
        }

        .filter-section h4 {
            font-size: 14px;
            color: var(--primary-color);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            background: linear-gradient(90deg, rgba(0, 212, 255, 0.12), rgba(0, 153, 204, 0.08), transparent);
            padding: 6px 10px;
            border-radius: 6px;
            border-left: 3px solid var(--primary-color);
        }

        .filter-buttons {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .filter-btn {
            background: rgba(0, 212, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.2);
            color: var(--text-secondary);
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-btn:hover {
            background: rgba(0, 212, 255, 0.1);
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateX(5px);
        }

        .filter-btn.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-color: var(--primary-color);
            color: var(--bg-primary);
            font-weight: 600;
        }

        .date-range-inputs {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .date-input {
            background: rgba(0, 212, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.2);
            color: var(--text-primary);
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-family: var(--font-mono);
        }

        .date-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }

        /* 右侧事件列表 */
        .events-panel {
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-primary);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .events-header {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.2) 0%,
                rgba(0, 153, 204, 0.2) 100%);
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .events-header h3 {
            margin: 0;
            font-size: 18px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .events-stats {
            display: flex;
            gap: 15px;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stat-count {
            font-weight: bold;
            color: var(--primary-color);
            font-family: var(--font-mono);
        }

        /* 事件列表表头 */
        .events-table-header {
            display: grid;
            grid-template-columns: 50px 100px 80px 80px 1fr;
            gap: 8px;
            padding: 12px 20px;
            background: rgba(0, 212, 255, 0.1);
            border-bottom: 1px solid var(--border-color);
            font-size: 13px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .header-cell {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .header-cell.serial {
            justify-content: flex-end;
            padding-right: 8px;
        }

        /* 事件列表内容 */
        .events-content {
            flex: 1;
            overflow-y: auto;
            padding: 0;
        }

        .event-item {
            display: grid;
            grid-template-columns: 50px 100px 80px 80px 1fr;
            gap: 8px;
            padding: 10px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            align-items: center;
            font-size: 12px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .event-item:hover {
            background: rgba(0, 212, 255, 0.05);
            border-left: 3px solid var(--primary-color);
            padding-left: 17px;
        }

        .event-serial {
            text-align: right;
            font-weight: bold;
            font-family: var(--font-mono);
            padding-right: 8px;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
        }

        .event-date {
            font-family: var(--font-mono);
            font-size: 11px;
        }

        .event-time {
            font-family: var(--font-mono);
            font-size: 11px;
        }

        .event-device {
            font-weight: 500;
            color: var(--primary-color);
        }

        .event-message {
            color: var(--text-secondary);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* 不同类型事件的颜色 */
        .event-item.alarm .event-serial,
        .event-item.alarm .event-date,
        .event-item.alarm .event-time {
            color: var(--warning-color);
        }

        .event-item.fault .event-serial,
        .event-item.fault .event-date,
        .event-item.fault .event-time {
            color: var(--error-color);
        }

        .event-item.recovery .event-serial,
        .event-item.recovery .event-date,
        .event-item.recovery .event-time {
            color: var(--text-primary);
        }

        /* 空状态显示 */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 300px;
            color: var(--text-muted);
        }

        .empty-state i {
            font-size: 48px;
            color: var(--primary-color);
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .empty-state p {
            font-size: 16px;
            margin: 0;
        }

        /* 滚动条样式优化 */
        .filter-content::-webkit-scrollbar,
        .events-content::-webkit-scrollbar {
            width: 6px;
        }

        .filter-content::-webkit-scrollbar-track,
        .events-content::-webkit-scrollbar-track {
            background: rgba(0, 212, 255, 0.1);
            border-radius: 3px;
        }

        .filter-content::-webkit-scrollbar-thumb,
        .events-content::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
            border-radius: 3px;
        }

        .filter-content::-webkit-scrollbar-thumb:hover,
        .events-content::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, var(--accent-color), var(--primary-color));
        }

        /* 响应式优化 */
        @media (max-width: 1400px) {
            .history-events-container {
                width: 100%;
                max-width: 1366px;
            }

            .history-main {
                grid-template-columns: 280px 1fr;
                gap: 15px;
                padding: 15px;
            }
        }

        @media (max-width: 1200px) {
            .history-main {
                grid-template-columns: 1fr;
                grid-template-rows: auto 1fr;
            }

            .filter-panel {
                height: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="history-events-container">
        <!-- 顶部标题栏 -->
        <header class="history-header" style="display: none;">
            <div class="history-title">
                <i class="fas fa-calendar-alt"></i>
                <h1>历史事件</h1>
            </div>
            <div class="history-time" id="currentTime">
                2025-01-01 00:00:00
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="history-main">
            <!-- 左侧筛选面板 -->
            <section class="filter-panel">
                <div class="filter-header">
                    <h3><i class="fas fa-filter"></i>筛选条件</h3>
                </div>
                <div class="filter-content">
                    <!-- 事件类型筛选 -->
                    <div class="filter-section">
                        <h4><i class="fas fa-tags"></i>事件类型</h4>
                        <div class="filter-buttons">
                            <button class="filter-btn active" data-type="all">
                                <i class="fas fa-list"></i>
                                <span>所有事件</span>
                            </button>
                            <button class="filter-btn" data-type="alarm">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>告警事件</span>
                            </button>
                            <button class="filter-btn" data-type="fault">
                                <i class="fas fa-times-circle"></i>
                                <span>故障事件</span>
                            </button>
                            <button class="filter-btn" data-type="recovery">
                                <i class="fas fa-check-circle"></i>
                                <span>恢复事件</span>
                            </button>
                        </div>
                    </div>

                    <!-- 时间范围筛选 -->
                    <div class="filter-section">
                        <h4><i class="fas fa-clock"></i>时间范围</h4>
                        <div class="date-range-inputs">
                            <input type="datetime-local" class="date-input" id="startDate" placeholder="开始时间">
                            <input type="datetime-local" class="date-input" id="endDate" placeholder="结束时间">
                            <button class="filter-btn" onclick="applyDateFilter()">
                                <i class="fas fa-search"></i>
                                <span>应用筛选</span>
                            </button>
                        </div>
                    </div>

                    <!-- 设备筛选 -->
                    <div class="filter-section">
                        <h4><i class="fas fa-microchip"></i>设备类型</h4>
                        <div class="filter-buttons">
                            <button class="filter-btn" data-device="all">
                                <i class="fas fa-server"></i>
                                <span>所有设备</span>
                            </button>
                            <button class="filter-btn" data-device="svg">
                                <i class="fas fa-bolt"></i>
                                <span>SVG系统</span>
                            </button>
                            <button class="filter-btn" data-device="cooling">
                                <i class="fas fa-tint"></i>
                                <span>水冷系统</span>
                            </button>
                            <button class="filter-btn" data-device="communication">
                                <i class="fas fa-wifi"></i>
                                <span>通信模块</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 右侧事件列表 -->
            <section class="events-panel">
                <div class="events-header">
                    <h3><i class="fas fa-history"></i>历史事件记录</h3>
                    <div class="events-stats">
                        <div class="stat-item">
                            <i class="fas fa-list"></i>
                            <span>总计: <span class="stat-count" id="totalCount">0</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-exclamation-triangle" style="color: var(--warning-color);"></i>
                            <span>告警: <span class="stat-count" id="alarmCount">0</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-times-circle" style="color: var(--error-color);"></i>
                            <span>故障: <span class="stat-count" id="faultCount">0</span></span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-check-circle" style="color: var(--success-color);"></i>
                            <span>恢复: <span class="stat-count" id="recoveryCount">0</span></span>
                        </div>
                    </div>
                </div>

                <!-- 表头 -->
                <div class="events-table-header">
                    <div class="header-cell serial">
                        <i class="fas fa-hashtag"></i>
                        <span>序号</span>
                    </div>
                    <div class="header-cell">
                        <i class="fas fa-calendar"></i>
                        <span>日期</span>
                    </div>
                    <div class="header-cell">
                        <i class="fas fa-clock"></i>
                        <span>时间</span>
                    </div>
                    <div class="header-cell">
                        <i class="fas fa-microchip"></i>
                        <span>设备</span>
                    </div>
                    <div class="header-cell">
                        <i class="fas fa-info-circle"></i>
                        <span>事件信息</span>
                    </div>
                </div>

                <!-- 事件列表内容 -->
                <div class="events-content" id="eventsContent">
                    <!-- 事件项将通过JavaScript动态生成 -->
                </div>
            </section>
        </main>
    </div>

    <script>
        /**
         * 桂林智源 SVG 数字化系统 - 历史事件页面脚本
         * 处理历史事件数据展示、筛选和交互功能
         */

        // 全局变量
        let allEvents = []; // 存储所有历史事件
        let filteredEvents = []; // 存储筛选后的事件
        let currentFilter = {
            type: 'all',
            device: 'all',
            startDate: null,
            endDate: null
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('历史事件页面初始化开始...');
            initHistoryPage();
            updateTime();
            setInterval(updateTime, 1000);
            console.log('历史事件页面初始化完成');
        });

        /**
         * 初始化历史事件页面
         */
        function initHistoryPage() {
            initEventFilters();
            generateHistoryEvents();
            renderEventsList();
            updateStatistics();
        }

        /**
         * 更新时间显示
         */
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        /**
         * 初始化事件筛选器
         */
        function initEventFilters() {
            // 事件类型筛选按钮
            const typeButtons = document.querySelectorAll('[data-type]');
            typeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除其他按钮的active状态
                    typeButtons.forEach(btn => btn.classList.remove('active'));
                    // 添加当前按钮的active状态
                    this.classList.add('active');
                    // 更新筛选条件
                    currentFilter.type = this.dataset.type;
                    applyFilters();
                });
            });

            // 设备类型筛选按钮
            const deviceButtons = document.querySelectorAll('[data-device]');
            deviceButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 切换按钮状态
                    if (this.classList.contains('active')) {
                        this.classList.remove('active');
                        currentFilter.device = 'all';
                    } else {
                        deviceButtons.forEach(btn => btn.classList.remove('active'));
                        this.classList.add('active');
                        currentFilter.device = this.dataset.device;
                    }
                    applyFilters();
                });
            });

            // 设置默认日期范围（最近7天）
            const now = new Date();
            const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

            document.getElementById('startDate').value = formatDateTimeLocal(sevenDaysAgo);
            document.getElementById('endDate').value = formatDateTimeLocal(now);
        }

        /**
         * 格式化日期时间为本地输入格式
         * @param {Date} date - 日期对象
         * @returns {string} 格式化的日期时间字符串
         */
        function formatDateTimeLocal(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            return `${year}-${month}-${day}T${hours}:${minutes}`;
        }

        /**
         * 生成历史事件数据（模拟数据）
         * 创建150条涵盖最近30天的历史事件记录
         * 包含告警、故障、恢复三种类型的事件
         * 涉及SVG系统、水冷系统、通信模块等多种设备
         */
        function generateHistoryEvents() {
            const eventTypes = ['alarm', 'fault', 'recovery'];
            const devices = [
                { name: 'SVG主控', category: 'svg' },
                { name: 'IGBT模块', category: 'svg' },
                { name: '电压调节', category: 'svg' },
                { name: '功率监测', category: 'svg' },
                { name: '直流母线', category: 'svg' },
                { name: '水冷系统', category: 'cooling' },
                { name: '冷却泵', category: 'cooling' },
                { name: '温度传感器', category: 'cooling' },
                { name: '通信模块', category: 'communication' },
                { name: '数据采集', category: 'communication' },
                { name: '网络接口', category: 'communication' }
            ];

            const messages = {
                alarm: [
                    '设备温度略高，已自动调节',
                    '功率因数偏低，建议检查负载',
                    '直流母线电压波动',
                    '通信延迟增加',
                    '设备运行参数超出正常范围',
                    '冷却系统压力异常',
                    '电压谐波含量偏高',
                    '负载不平衡告警'
                ],
                fault: [
                    '设备过热故障，已切换备用',
                    '通信链路中断，正在重连',
                    '电源模块异常，已启动保护',
                    '传感器故障，数据异常',
                    '控制器响应超时',
                    '冷却系统泄漏检测',
                    'IGBT模块过流保护',
                    '系统自检失败'
                ],
                recovery: [
                    'SVG系统自检完成',
                    '电压参数调整完成',
                    '系统故障恢复，运行正常',
                    '通信链路检测正常',
                    '设备温度恢复正常',
                    '冷却系统运行稳定',
                    '电源模块恢复正常',
                    '数据采集功能正常'
                ]
            };

            // 生成最近30天的历史事件
            const now = new Date();
            allEvents = [];

            for (let i = 0; i < 150; i++) {
                // 随机生成过去30天内的时间
                const randomDays = Math.random() * 30;
                const randomHours = Math.random() * 24;
                const randomMinutes = Math.random() * 60;
                const eventTime = new Date(now.getTime() - (randomDays * 24 * 60 * 60 * 1000) - (randomHours * 60 * 60 * 1000) - (randomMinutes * 60 * 1000));

                const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)];
                const device = devices[Math.floor(Math.random() * devices.length)];
                const message = messages[eventType][Math.floor(Math.random() * messages[eventType].length)];

                allEvents.push({
                    id: i + 1,
                    type: eventType,
                    device: device.name,
                    deviceCategory: device.category,
                    message: message,
                    timestamp: eventTime.getTime(),
                    date: formatDate(eventTime),
                    time: formatTime(eventTime)
                });
            }

            // 按时间戳降序排序（最新的在前）
            allEvents.sort((a, b) => b.timestamp - a.timestamp);

            // 重新分配序号
            allEvents.forEach((event, index) => {
                event.serial = index + 1;
            });

            console.log(`生成了 ${allEvents.length} 条历史事件记录`);
        }

        /**
         * 格式化日期
         * @param {Date} date - 日期对象
         * @returns {string} 格式化的日期字符串
         */
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        /**
         * 格式化时间
         * @param {Date} date - 日期对象
         * @returns {string} 格式化的时间字符串
         */
        function formatTime(date) {
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            return `${hours}:${minutes}:${seconds}`;
        }

        /**
         * 应用筛选条件
         */
        function applyFilters() {
            filteredEvents = allEvents.filter(event => {
                // 事件类型筛选
                if (currentFilter.type !== 'all' && event.type !== currentFilter.type) {
                    return false;
                }

                // 设备类型筛选
                if (currentFilter.device !== 'all' && event.deviceCategory !== currentFilter.device) {
                    return false;
                }

                // 时间范围筛选
                if (currentFilter.startDate && event.timestamp < currentFilter.startDate) {
                    return false;
                }
                if (currentFilter.endDate && event.timestamp > currentFilter.endDate) {
                    return false;
                }

                return true;
            });

            renderEventsList();
            updateStatistics();
        }

        /**
         * 应用日期筛选
         */
        function applyDateFilter() {
            const startDateInput = document.getElementById('startDate');
            const endDateInput = document.getElementById('endDate');

            if (startDateInput.value) {
                currentFilter.startDate = new Date(startDateInput.value).getTime();
            } else {
                currentFilter.startDate = null;
            }

            if (endDateInput.value) {
                currentFilter.endDate = new Date(endDateInput.value).getTime();
            } else {
                currentFilter.endDate = null;
            }

            applyFilters();
        }

        /**
         * 渲染事件列表
         */
        function renderEventsList() {
            const eventsContent = document.getElementById('eventsContent');

            if (filteredEvents.length === 0) {
                eventsContent.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <p>暂无符合条件的历史事件</p>
                    </div>
                `;
                return;
            }

            const eventsHtml = filteredEvents.map((event, index) => `
                <div class="event-item ${event.type}" data-event-id="${event.id}">
                    <span class="event-serial">${index + 1}</span>
                    <span class="event-date">${event.date}</span>
                    <span class="event-time">${event.time}</span>
                    <span class="event-device">${event.device}</span>
                    <span class="event-message" title="${event.message}">${event.message}</span>
                </div>
            `).join('');

            eventsContent.innerHTML = eventsHtml;

            // 添加点击事件监听
            const eventItems = eventsContent.querySelectorAll('.event-item');
            eventItems.forEach(item => {
                item.addEventListener('click', function() {
                    const eventId = parseInt(this.dataset.eventId);
                    showEventDetails(eventId);
                });
            });
        }

        /**
         * 更新统计信息
         */
        function updateStatistics() {
            const totalCount = filteredEvents.length;
            const alarmCount = filteredEvents.filter(event => event.type === 'alarm').length;
            const faultCount = filteredEvents.filter(event => event.type === 'fault').length;
            const recoveryCount = filteredEvents.filter(event => event.type === 'recovery').length;

            document.getElementById('totalCount').textContent = totalCount;
            document.getElementById('alarmCount').textContent = alarmCount;
            document.getElementById('faultCount').textContent = faultCount;
            document.getElementById('recoveryCount').textContent = recoveryCount;
        }

        /**
         * 显示事件详情（可扩展功能）
         * @param {number} eventId - 事件ID
         */
        function showEventDetails(eventId) {
            const event = allEvents.find(e => e.id === eventId);
            if (event) {
                console.log('显示事件详情:', event);
                // 这里可以扩展为显示详细信息的弹窗
                alert(`事件详情:\n设备: ${event.device}\n类型: ${getEventTypeName(event.type)}\n时间: ${event.date} ${event.time}\n描述: ${event.message}`);
            }
        }

        /**
         * 获取事件类型中文名称
         * @param {string} type - 事件类型
         * @returns {string} 中文名称
         */
        function getEventTypeName(type) {
            const typeNames = {
                'alarm': '告警事件',
                'fault': '故障事件',
                'recovery': '恢复事件'
            };
            return typeNames[type] || type;
        }
    </script>
</body>
</html>
