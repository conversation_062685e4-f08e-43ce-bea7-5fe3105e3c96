<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>桂林智源 SVG 数字化系统 - I/O状态监控</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /**
         * 桂林智源 SVG 数字化系统 - I/O状态监控页面样式
         * 深色科技主题，工业监控界面风格
         */

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #00d4ff;
            --secondary-color: #0099cc;
            --accent-color: #00ff88;
            --warning-color: #ffaa00;
            --error-color: #ff4444;
            --success-color: #00ff88;
            --bg-primary: #0a0f1c;
            --bg-secondary: #1a2332;
            --bg-tertiary: #2a3441;
            --text-primary: #ffffff;
            --text-secondary: #b8c5d6;
            --border-color: #3a4a5c;
            --shadow-color: rgba(0, 212, 255, 0.3);
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            width: 1366px;
            height: 768px;
            overflow: hidden;
            position: relative;
        }

        /* 背景动画效果 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 255, 136, 0.1) 0%, transparent 50%);
            animation: backgroundPulse 4s ease-in-out infinite alternate;
            pointer-events: none;
        }

        @keyframes backgroundPulse {
            0% { opacity: 0.3; }
            100% { opacity: 0.6; }
        }

        .container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 1;
        }

        /* 页面头部 */
        .page-header {
            background: linear-gradient(90deg, var(--bg-secondary), var(--bg-tertiary));
            border-bottom: 2px solid var(--primary-color);
            padding: 15px 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0, 212, 255, 0.3);
        }

        .page-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .page-title i {
            font-size: 28px;
            color: var(--accent-color);
        }

        .current-time {
            font-size: 16px;
            color: var(--text-secondary);
            font-family: 'Consolas', monospace;
        }

        /* 主要内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            gap: 20px;
        }

        /* 状态概览 */
        .status-overview {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .status-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .status-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-color);
        }

        .status-card.success::before { background: var(--success-color); }
        .status-card.warning::before { background: var(--warning-color); }
        .status-card.error::before { background: var(--error-color); }

        .status-value {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .status-label {
            font-size: 14px;
            color: var(--text-secondary);
        }

        /* I/O状态网格 */
        .io-status-container {
            flex: 1;
            background: var(--bg-secondary);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .io-status-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            border-radius: 12px 12px 0 0;
        }

        .io-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .io-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .refresh-btn {
            padding: 8px 16px;
            background: var(--bg-tertiary);
            border: 1px solid var(--primary-color);
            border-radius: 6px;
            color: var(--primary-color);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .refresh-btn:hover {
            background: var(--primary-color);
            color: var(--bg-primary);
            box-shadow: 0 2px 8px rgba(0, 212, 255, 0.4);
        }

        .io-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 12px;
            flex: 1;
            align-content: start;
        }

        .io-item {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .io-item:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(0, 212, 255, 0.3);
        }

        .io-indicator {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin: 0 auto 8px;
            transition: all 0.3s ease;
            position: relative;
        }

        .io-indicator.active {
            background: var(--success-color);
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }

        .io-indicator.inactive {
            background: var(--border-color);
        }

        .io-indicator.error {
            background: var(--error-color);
            box-shadow: 0 0 10px rgba(255, 68, 68, 0.5);
        }

        .io-indicator.warning {
            background: var(--warning-color);
            box-shadow: 0 0 10px rgba(255, 170, 0, 0.5);
        }

        .io-label {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .io-value {
            font-size: 11px;
            color: var(--text-primary);
            font-family: 'Consolas', monospace;
        }

        /* 响应式设计 */
        @media (max-width: 1366px) {
            body {
                width: 100vw;
                height: 100vh;
            }
            
            .page-header {
                padding: 12px 20px;
            }
            
            .main-content {
                padding: 15px;
            }
            
            .io-grid {
                grid-template-columns: repeat(6, 1fr);
            }
        }

        /* 动画效果 */
        .io-indicator.active {
            animation: activePulse 2s ease-in-out infinite;
        }

        @keyframes activePulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .io-indicator.error {
            animation: errorBlink 1s ease-in-out infinite;
        }

        @keyframes errorBlink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <header class="page-header" style="display: none;">
            <div class="page-title">
                <i class="fas fa-plug"></i>
                <span>I/O状态监控</span>
            </div>
            <div class="current-time" id="currentTime">
                <!-- 当前时间将通过JavaScript动态更新 -->
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 状态概览 -->
            <section class="status-overview">
                <div class="status-card success">
                    <div class="status-value" id="activeCount">0</div>
                    <div class="status-label">正常运行</div>
                </div>
                <div class="status-card warning">
                    <div class="status-value" id="warningCount">0</div>
                    <div class="status-label">告警状态</div>
                </div>
                <div class="status-card error">
                    <div class="status-value" id="errorCount">0</div>
                    <div class="status-label">故障状态</div>
                </div>
                <div class="status-card">
                    <div class="status-value" id="totalCount">0</div>
                    <div class="status-label">总计数量</div>
                </div>
            </section>

            <!-- I/O状态网格 -->
            <section class="io-status-container">
                <div class="io-header">
                    <div class="io-title">
                        <i class="fas fa-microchip"></i>
                        <span>I/O端口状态</span>
                    </div>
                    <button class="refresh-btn" onclick="refreshIOStatus()">
                        <i class="fas fa-sync-alt"></i>
                        <span>刷新状态</span>
                    </button>
                </div>

                <div class="io-grid" id="ioGrid">
                    <!-- I/O状态项将通过JavaScript动态生成 -->
                </div>
            </section>
        </main>
    </div>

    <script>
        /**
         * 桂林智源 SVG 数字化系统 - I/O状态监控页面脚本
         * 处理I/O状态数据展示、实时更新和交互功能
         */

        // 全局变量
        let ioData = [];
        let updateInterval = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('I/O状态监控页面初始化开始...');
            initIOStatusPage();
            updateTime();
            setInterval(updateTime, 1000);
            console.log('I/O状态监控页面初始化完成');
        });

        /**
         * 初始化I/O状态页面
         */
        function initIOStatusPage() {
            generateIOData();
            renderIOGrid();
            updateStatusOverview();
            startRealTimeUpdate();
        }

        /**
         * 更新时间显示
         */
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        /**
         * 生成I/O数据
         */
        function generateIOData() {
            ioData = [];
            
            // 生成数字输入端口
            for (let i = 1; i <= 16; i++) {
                ioData.push({
                    id: `DI${i.toString().padStart(2, '0')}`,
                    type: 'input',
                    label: `数字输入${i}`,
                    status: Math.random() > 0.3 ? 'active' : (Math.random() > 0.8 ? 'error' : 'inactive'),
                    value: Math.random() > 0.5 ? '1' : '0'
                });
            }
            
            // 生成数字输出端口
            for (let i = 1; i <= 16; i++) {
                ioData.push({
                    id: `DO${i.toString().padStart(2, '0')}`,
                    type: 'output',
                    label: `数字输出${i}`,
                    status: Math.random() > 0.2 ? 'active' : (Math.random() > 0.9 ? 'warning' : 'inactive'),
                    value: Math.random() > 0.4 ? '1' : '0'
                });
            }
            
            // 生成模拟输入端口
            for (let i = 1; i <= 8; i++) {
                ioData.push({
                    id: `AI${i.toString().padStart(2, '0')}`,
                    type: 'analog_input',
                    label: `模拟输入${i}`,
                    status: Math.random() > 0.1 ? 'active' : 'warning',
                    value: (Math.random() * 10).toFixed(2) + 'V'
                });
            }
            
            // 生成模拟输出端口
            for (let i = 1; i <= 8; i++) {
                ioData.push({
                    id: `AO${i.toString().padStart(2, '0')}`,
                    type: 'analog_output',
                    label: `模拟输出${i}`,
                    status: Math.random() > 0.15 ? 'active' : 'inactive',
                    value: (Math.random() * 10).toFixed(2) + 'V'
                });
            }
        }

        /**
         * 渲染I/O网格
         */
        function renderIOGrid() {
            const grid = document.getElementById('ioGrid');
            
            const html = ioData.map(item => `
                <div class="io-item" data-id="${item.id}">
                    <div class="io-indicator ${item.status}"></div>
                    <div class="io-label">${item.id}</div>
                    <div class="io-value">${item.value}</div>
                </div>
            `).join('');
            
            grid.innerHTML = html;
        }

        /**
         * 更新状态概览
         */
        function updateStatusOverview() {
            const activeCount = ioData.filter(item => item.status === 'active').length;
            const warningCount = ioData.filter(item => item.status === 'warning').length;
            const errorCount = ioData.filter(item => item.status === 'error').length;
            const totalCount = ioData.length;
            
            document.getElementById('activeCount').textContent = activeCount;
            document.getElementById('warningCount').textContent = warningCount;
            document.getElementById('errorCount').textContent = errorCount;
            document.getElementById('totalCount').textContent = totalCount;
        }

        /**
         * 开始实时更新
         */
        function startRealTimeUpdate() {
            updateInterval = setInterval(() => {
                updateIOStatus();
                renderIOGrid();
                updateStatusOverview();
            }, 3000);
        }

        /**
         * 更新I/O状态
         */
        function updateIOStatus() {
            ioData.forEach(item => {
                // 随机更新状态
                if (Math.random() > 0.9) {
                    const statuses = ['active', 'inactive', 'warning', 'error'];
                    const weights = [0.6, 0.2, 0.15, 0.05]; // 权重分布
                    
                    let random = Math.random();
                    let cumulativeWeight = 0;
                    
                    for (let i = 0; i < statuses.length; i++) {
                        cumulativeWeight += weights[i];
                        if (random <= cumulativeWeight) {
                            item.status = statuses[i];
                            break;
                        }
                    }
                }
                
                // 更新值
                if (item.type === 'input' || item.type === 'output') {
                    item.value = Math.random() > 0.5 ? '1' : '0';
                } else {
                    item.value = (Math.random() * 10).toFixed(2) + 'V';
                }
            });
        }

        /**
         * 刷新I/O状态
         */
        function refreshIOStatus() {
            console.log('刷新I/O状态');
            
            // 停止当前更新
            if (updateInterval) {
                clearInterval(updateInterval);
            }
            
            // 重新生成数据
            generateIOData();
            renderIOGrid();
            updateStatusOverview();
            
            // 重新开始更新
            startRealTimeUpdate();
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        });
    </script>
</body>
</html>
