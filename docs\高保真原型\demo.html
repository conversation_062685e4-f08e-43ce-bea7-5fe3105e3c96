<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高保真原型演示 - 白云电气数字孪生系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .demo-container {
            max-width: 800px;
            padding: 40px;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 60px rgba(0, 212, 255, 0.2);
            text-align: center;
        }

        .logo {
            font-size: 48px;
            color: #00d4ff;
            margin-bottom: 20px;
            animation: glow 3s ease-in-out infinite alternate;
        }

        .title {
            font-size: 32px;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #00d4ff, #66e0ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 18px;
            color: #b8c5d6;
            margin-bottom: 40px;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .feature {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 8px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .feature:hover {
            background: rgba(0, 212, 255, 0.2);
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
        }

        .feature-icon {
            font-size: 32px;
            color: #00d4ff;
            margin-bottom: 10px;
        }

        .feature-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .feature-desc {
            font-size: 14px;
            color: #b8c5d6;
            line-height: 1.4;
        }

        .demo-button {
            display: inline-block;
            background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
            color: #0a0e1a;
            padding: 15px 40px;
            border-radius: 8px;
            text-decoration: none;
            font-size: 18px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .demo-button:hover {
            background: linear-gradient(135deg, #66e0ff 0%, #00d4ff 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.5);
        }

        .demo-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
            transition: left 0.5s ease;
        }

        .demo-button:hover::before {
            left: 100%;
        }

        .instructions {
            margin-top: 30px;
            padding: 20px;
            background: rgba(0, 212, 255, 0.05);
            border-left: 4px solid #00d4ff;
            border-radius: 0 8px 8px 0;
            text-align: left;
        }

        .instructions h3 {
            color: #00d4ff;
            margin-bottom: 15px;
        }

        .instructions ul {
            list-style: none;
            padding-left: 0;
        }

        .instructions li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
            color: #b8c5d6;
        }

        .instructions li::before {
            content: '▶';
            position: absolute;
            left: 0;
            color: #00d4ff;
        }

        @keyframes glow {
            0% {
                text-shadow: 0 0 10px #00d4ff;
            }
            100% {
                text-shadow: 0 0 20px #00d4ff, 0 0 30px #00d4ff;
            }
        }

        .tech-specs {
            margin-top: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .spec-item {
            background: rgba(0, 212, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.1);
            border-radius: 6px;
            padding: 15px;
            text-align: center;
        }

        .spec-value {
            font-size: 20px;
            font-weight: bold;
            color: #00d4ff;
            margin-bottom: 5px;
        }

        .spec-label {
            font-size: 12px;
            color: #b8c5d6;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        @media (max-width: 768px) {
            .demo-container {
                margin: 20px;
                padding: 30px 20px;
            }
            
            .title {
                font-size: 24px;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="logo">⚡</div>
        <h1 class="title">白云电气数字孪生系统</h1>
        <p class="subtitle">高保真原型演示</p>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🎨</div>
                <div class="feature-title">科技感UI</div>
                <div class="feature-desc">深色主题配合蓝色科技风格，霓虹灯效果和渐变动画</div>
            </div>
            <div class="feature">
                <div class="feature-icon">🔧</div>
                <div class="feature-title">3D可视化</div>
                <div class="feature-desc">Unity WebGL集成，设备模型展示和交互控制</div>
            </div>
            <div class="feature">
                <div class="feature-icon">📊</div>
                <div class="feature-title">实时监控</div>
                <div class="feature-desc">ECharts图表，MODBUS数据模拟，实时状态更新</div>
            </div>
            <div class="feature">
                <div class="feature-icon">✨</div>
                <div class="feature-title">动画效果</div>
                <div class="feature-desc">流畅的CSS3动画，交互反馈和数据流动效果</div>
            </div>
        </div>

        <a href="index.html" class="demo-button">
            🚀 查看高保真原型
        </a>

        <div class="tech-specs">
            <div class="spec-item">
                <div class="spec-value">HTML5</div>
                <div class="spec-label">标准</div>
            </div>
            <div class="spec-item">
                <div class="spec-value">CSS3</div>
                <div class="spec-label">样式</div>
            </div>
            <div class="spec-item">
                <div class="spec-value">ECharts</div>
                <div class="spec-label">图表</div>
            </div>
            <div class="spec-item">
                <div class="spec-value">Unity</div>
                <div class="spec-label">3D引擎</div>
            </div>
        </div>

        <div class="instructions">
            <h3>使用说明</h3>
            <ul>
                <li>点击上方按钮进入高保真原型界面</li>
                <li>左侧面板控制3D视角和设备状态</li>
                <li>中央区域展示Unity WebGL 3D模型</li>
                <li>右侧面板显示实时数据和图表</li>
                <li>所有数据都是模拟生成，每2-5秒自动更新</li>
                <li>支持键盘快捷键：ESC关闭弹窗，F11全屏</li>
                <li>最佳体验分辨率：1920x1080或更高</li>
            </ul>
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为特性卡片添加随机延迟动画
            const features = document.querySelectorAll('.feature');
            features.forEach((feature, index) => {
                feature.style.animationDelay = (index * 0.2) + 's';
                feature.style.animation = 'fadeInUp 0.6s ease-out both';
            });

            // 添加鼠标跟踪效果
            document.addEventListener('mousemove', function(e) {
                const container = document.querySelector('.demo-container');
                const rect = container.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                
                const rotateX = (y - centerY) / centerY * 2;
                const rotateY = (centerX - x) / centerX * 2;
                
                container.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
            });

            // 鼠标离开时重置
            document.addEventListener('mouseleave', function() {
                const container = document.querySelector('.demo-container');
                container.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg)';
            });
        });

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                0% {
                    opacity: 0;
                    transform: translateY(30px);
                }
                100% {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
