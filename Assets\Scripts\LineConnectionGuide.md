# 3D物体与UI连线功能使用指南

## 概述

本功能实现了3D物体开关与2D Canvas上的说明与连线，在相机旋转过程中保持连线正确。主要包括两个核心脚本：

1. `LineManager.cs` - 核心连线管理器，负责处理连线的位置更新和渲染
2. `LineConnector.cs` - 连线连接器，用于在设备控制器中集成连线管理功能

## 功能特点

- 自动更新连线位置，确保在相机旋转时连线始终正确连接3D物体和UI元素
- 支持自定义连线颜色、宽度和偏移量
- 可以动态控制连线的显示和隐藏
- 当3D物体不在相机视野内时，可以自动隐藏连线
- 支持运行时动态添加和移除连线

## 使用步骤

### 1. 准备连线预制体

1. 在Unity编辑器中，创建一个UI Image对象作为连线预制体
   - 右键点击Canvas > UI > Image
   - 将Image重命名为"Line"
   - 设置Image的Pivot为(0.5, 0.5)，确保旋转时以中心为轴
   - 调整Image的宽度和高度（建议宽度设为100，高度设为2）
   - 将Image的颜色设置为白色或其他所需颜色

2. 将Line对象制作为预制体
   - 将Line对象拖拽到Project窗口中的Prefabs文件夹

### 2. 设置LineManager

1. 在场景中创建一个空物体，命名为"LineManager"
2. 添加LineManager组件
   - 选择LineManager物体 > Add Component > LineManager
3. 设置LineManager组件
   - Target Canvas: 场景中的主Canvas
   - Main Camera: 场景中的主相机
   - 可以调整Update Frequency以优化性能（默认为30）

### 3. 设置LineConnector

1. 在设备根物体上添加LineConnector组件
   - 选择设备根物体 > Add Component > LineConnector
2. 设置LineConnector组件
   - Line Manager: 引用场景中的LineManager对象
   - Target Canvas: 场景中的主Canvas
3. 配置连接列表
   - 点击"Connections"下的"+"按钮添加新连接
   - 填写每个连接的配置：
     - Connection Name: 连接的唯一名称
     - Device Component: 设备上的3D开关或组件
     - UI Element: UI上的说明文本或图标
     - Line Prefab: 之前创建的连线预制体
     - Line Color: 连线颜色
     - Line Width: 连线宽度
     - Object Offset: 3D物体连接点偏移
     - UI Offset: UI元素连接点偏移

## 代码示例

### 在运行时动态添加连线

```csharp
// 获取LineConnector组件
LineConnector lineConnector = GetComponent<LineConnector>();

// 添加新连线
lineConnector.AddConnection(
    "新连线",                 // 连线名称
    transform.Find("开关"),   // 3D物体
    uiTextElement,           // UI元素
    linePrefab,              // 连线预制体
    Color.yellow,            // 连线颜色
    2f                       // 连线宽度
);
```

### 控制连线可见性

```csharp
// 获取LineConnector组件
LineConnector lineConnector = GetComponent<LineConnector>();

// 隐藏特定连线
lineConnector.SetConnectionVisibility("开关连线", false);

// 显示特定连线
lineConnector.SetConnectionVisibility("开关连线", true);

// 隐藏所有连线
lineConnector.SetAllConnectionsVisibility(false);

// 显示所有连线
lineConnector.SetAllConnectionsVisibility(true);
```

### 更新连线属性

```csharp
// 获取LineConnector组件
LineConnector lineConnector = GetComponent<LineConnector>();

// 更新连线颜色
lineConnector.UpdateConnectionColor("开关连线", Color.red);

// 更新连线宽度
lineConnector.UpdateConnectionWidth("开关连线", 3f);
```

## 性能优化建议

1. 适当降低更新频率（LineManager中的updateFrequency）可以减少CPU负担
2. 对于大量连线，考虑使用对象池管理连线预制体
3. 当设备处于远距离或不需要交互时，可以隐藏所有连线
4. 使用简单的线条图像作为连线预制体，避免使用复杂的图像

## 常见问题

1. **连线位置不正确**：确保Canvas的Render Mode设置为Screen Space - Camera或World Space，并且正确设置了Canvas的相机引用
2. **连线没有显示**：检查连线预制体是否有Image组件，以及连线的颜色是否可见
3. **连线闪烁或抖动**：尝试增加更新频率或检查3D物体是否有不必要的微小移动
4. **性能问题**：减少连线数量或降低更新频率，确保不在每帧更新所有连线

## 扩展功能

1. 添加连线动画效果，如虚线流动或颜色渐变
2. 实现连线的交互功能，如点击连线显示详细信息
3. 添加连线标签，在连线中间显示文本信息
4. 实现曲线连线，使用贝塞尔曲线代替直线