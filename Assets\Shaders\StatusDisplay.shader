Shader "Custom/StatusDisplay" {
    Properties {
        _Color ("Main Color", Color) = (0, 1, 0, 1)
        _EmissionColor ("Emission Color", Color) = (0, 1, 0, 1)
        _MainTex ("Albedo (RGB)", 2D) = "white" {}
        _EmissionMap ("Emission Map", 2D) = "white" {}
        _EmissionIntensity ("Emission Intensity", Range(0, 10)) = 2.0
        _Glossiness ("Smoothness", Range(0, 1)) = 0.5
        _Metallic ("Metallic", Range(0, 1)) = 0.0
    }
    
    SubShader {
        Tags { "RenderType"="Opaque" }
        LOD 200
        
        CGPROGRAM
        #pragma surface surf Standard fullforwardshadows
        #pragma target 3.0
        
        sampler2D _MainTex;
        sampler2D _EmissionMap;
        
        struct Input {
            float2 uv_MainTex;
            float2 uv_EmissionMap;
        };
        
        half _Glossiness;
        half _Metallic;
        fixed4 _Color;
        fixed4 _EmissionColor;
        float _EmissionIntensity;
        
        void surf (Input IN, inout SurfaceOutputStandard o) {
            // 基础颜色
            fixed4 c = tex2D(_MainTex, IN.uv_MainTex) * _Color;
            o.Albedo = c.rgb;
            
            // 金属度和光滑度
            o.Metallic = _Metallic;
            o.Smoothness = _Glossiness;
            
            // 自发光 - 用于闪烁和变色效果
            fixed4 e = tex2D(_EmissionMap, IN.uv_EmissionMap) * _EmissionColor * _EmissionIntensity;
            o.Emission = e.rgb;
            
            o.Alpha = c.a;
        }
        ENDCG
    }
    FallBack "Diffuse"
}