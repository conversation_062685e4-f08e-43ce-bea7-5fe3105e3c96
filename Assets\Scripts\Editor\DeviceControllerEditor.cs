using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// 设备控制器编辑器扩展 - 提供便捷的组件查找和设置功能
/// </summary>
[CustomEditor(typeof(DeviceController))]
public class DeviceControllerEditor : Editor
{
    private DeviceController deviceController;
    private bool showComponentSettings = true;
    private bool showAnimationSettings = true;
    private bool showEffectSettings = true;
    private bool showAutoFindOptions = true;

    private string logoNameFilter = "10kV SVG柜体模型.STEP-1"; //Logo
    private string switchNameFilter = "#switch"; //开关
    private string doorNameFilter = "#door"; //门
    private string statusDisplayNameFilter = "#statusDisplay"; //状态显示器
    private string dataDisplayNameFilter = "#dataDisplay"; //数据显示器

    void OnEnable()
    {
        deviceController = (DeviceController)target;
    }

    public override void OnInspectorGUI()
    {
        serializedObject.Update();

        EditorGUILayout.Space();
        EditorGUILayout.HelpBox("设备控制器用于管理设备的所有交互功能，包括元器件展开/收回、开关控制、门动画、水管流动效果和状态显示器效果。", MessageType.Info);
        EditorGUILayout.Space();

        // 自动查找组件选项
        showAutoFindOptions = EditorGUILayout.Foldout(showAutoFindOptions, "自动查找组件", true);
        if (showAutoFindOptions)
        {
            EditorGUI.indentLevel++;

            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            EditorGUILayout.LabelField("组件名称过滤器", EditorStyles.boldLabel);

            logoNameFilter = EditorGUILayout.TextField("Logo名称包含", logoNameFilter);
            switchNameFilter = EditorGUILayout.TextField("开关名称包含", switchNameFilter);
            doorNameFilter = EditorGUILayout.TextField("门名称包含", doorNameFilter);
            statusDisplayNameFilter = EditorGUILayout.TextField("状态显示器名称包含", statusDisplayNameFilter);
            dataDisplayNameFilter = EditorGUILayout.TextField("数据显示器名称包含", dataDisplayNameFilter);

            EditorGUILayout.Space();

            if (GUILayout.Button("查找所有组件"))
            {
                FindAllComponents();
            }

            EditorGUILayout.Space();

            if (GUILayout.Button("查找Logo"))
            {
                FindLogo();
            }

            if (GUILayout.Button("查找开关"))
            {
                FindSwitch();
            }

            if (GUILayout.Button("查找门"))
            {
                FindDoor();
            }


            if (GUILayout.Button("查找状态显示器"))
            {
                FindStatusDisplays();
            }

            if (GUILayout.Button("查找数据显示器"))
            {
                FindDataDisplays();
            }

            if (GUILayout.Button("查找可展开组件"))
            {
                FindExpandableComponents();
            }

            EditorGUILayout.EndVertical();

            EditorGUI.indentLevel--;
        }

        EditorGUILayout.Space();

        // 组件设置
        showComponentSettings = EditorGUILayout.Foldout(showComponentSettings, "设备组件", true);
        if (showComponentSettings)
        {
            EditorGUI.indentLevel++;

            EditorGUILayout.PropertyField(serializedObject.FindProperty("deviceLogo"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("deviceComponents"), true);
            EditorGUILayout.PropertyField(serializedObject.FindProperty("deviceSwitch"));
            // 修改为使用deviceDoors列表
            EditorGUILayout.PropertyField(serializedObject.FindProperty("deviceDoors"), true);
            EditorGUILayout.PropertyField(serializedObject.FindProperty("statusDisplays"), true);
            EditorGUILayout.PropertyField(serializedObject.FindProperty("dataDisplays"), true);

            EditorGUI.indentLevel--;
        }

        EditorGUILayout.Space();

        // 动画设置
        showAnimationSettings = EditorGUILayout.Foldout(showAnimationSettings, "动画设置", true);
        if (showAnimationSettings)
        {
            EditorGUI.indentLevel++;

            // EditorGUILayout.PropertyField(serializedObject.FindProperty("expandDuration"));
            // EditorGUILayout.PropertyField(serializedObject.FindProperty("expandDistance"));
            // 移除不存在的属性
            // EditorGUILayout.PropertyField(serializedObject.FindProperty("doorAnimDuration"));
            // EditorGUILayout.PropertyField(serializedObject.FindProperty("doorOpenAngle"));

            EditorGUI.indentLevel--;
        }

        EditorGUILayout.Space();

        // 效果设置
        showEffectSettings = EditorGUILayout.Foldout(showEffectSettings, "效果设置", true);
        if (showEffectSettings)
        {
            EditorGUI.indentLevel++;

            EditorGUILayout.PropertyField(serializedObject.FindProperty("waterFlowMaterial"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("blinkFrequency"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("normalColor"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("warningColor"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("errorColor"));

            EditorGUI.indentLevel--;
        }

        EditorGUILayout.Space();

        // 测试按钮
        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("测试展开/收回"))
        {
            if (Application.isPlaying)
            {
                deviceController.ToggleExpand();
            }
            else
            {
                EditorUtility.DisplayDialog("提示", "请在运行模式下测试此功能", "确定");
            }
        }

        if (GUILayout.Button("测试开关"))
        {
            if (Application.isPlaying)
            {
                deviceController.ToggleSwitch();
            }
            else
            {
                EditorUtility.DisplayDialog("提示", "请在运行模式下测试此功能", "确定");
            }
        }

        if (GUILayout.Button("测试门"))
        {
            if (Application.isPlaying)
            {
                deviceController.ToggleDoor();
            }
            else
            {
                EditorUtility.DisplayDialog("提示", "请在运行模式下测试此功能", "确定");
            }
        }

        EditorGUILayout.EndHorizontal();

        serializedObject.ApplyModifiedProperties();
    }

    /// <summary>
    /// 查找所有组件
    /// </summary>
    private void FindAllComponents()
    {
        FindLogo();
        FindSwitch();
        FindDoor();
        FindStatusDisplays();
        FindDataDisplays();
        FindExpandableComponents();

        EditorUtility.SetDirty(deviceController);
    }

    /// <summary>
    /// 查找Logo
    /// </summary>
    private void FindLogo()
    {
        if (string.IsNullOrEmpty(logoNameFilter)) return;

        Transform[] allTransforms = deviceController.GetComponentsInChildren<Transform>(true);
        foreach (Transform t in allTransforms)
        {
            if (t.name.ToLower().Contains(logoNameFilter.ToLower()))
            {
                deviceController.deviceLogo = t;
                Debug.Log($"找到Logo: {t.name}");
                break;
            }
        }
    }

    /// <summary>
    /// 查找开关
    /// </summary>
    private void FindSwitch()
    {
        if (string.IsNullOrEmpty(switchNameFilter)) return;

        Transform[] allTransforms = deviceController.GetComponentsInChildren<Transform>(true);
        foreach (Transform t in allTransforms)
        {
            if (t.name.Contains(switchNameFilter))
            {
                deviceController.deviceSwitch = t;
                Debug.Log($"找到开关: {t.name}");
                break;
            }
        }
    }

    /// <summary>
    /// 查找门
    /// </summary>
    private void FindDoor()
    {
        if (string.IsNullOrEmpty(doorNameFilter)) return;

        deviceController.deviceDoors = deviceController.GetComponentsInChildren<Transform>(true)
            .Where(t => t.name.Contains(doorNameFilter))
            .Distinct()
            .ToList();

        Debug.Log($"找到 {deviceController.deviceDoors.Count} 个门组件");
    }



    /// <summary>
    /// 查找状态显示器
    /// </summary>
    private void FindStatusDisplays()
    {
        if (string.IsNullOrEmpty(statusDisplayNameFilter)) return;

        deviceController.statusDisplays.Clear();

        Renderer[] allRenderers = deviceController.GetComponentsInChildren<Renderer>(true);
        foreach (Renderer r in allRenderers)
        {
            if (r.name.Contains(statusDisplayNameFilter))
            {
                deviceController.statusDisplays.Add(r);
                Debug.Log($"找到状态显示器: {r.name}");
            }
        }
    }

    /// <summary>
    /// 查找数据显示器
    /// </summary>
    private void FindDataDisplays()
    {
        if (string.IsNullOrEmpty(dataDisplayNameFilter)) return;

        deviceController.dataDisplays.Clear();

        // 查找所有包含数据显示器名称的对象
        Transform[] allTransforms = deviceController.GetComponentsInChildren<Transform>(true);
        foreach (Transform t in allTransforms)
        {
            if (t.name.Contains(dataDisplayNameFilter))
            {
                // 首先检查是否已经有Canvas组件
                Canvas existingCanvas = t.GetComponent<Canvas>();
                if (existingCanvas != null)
                {
                    deviceController.dataDisplays.Add(existingCanvas);
                    Debug.Log($"找到数据显示器Canvas: {t.name}");

                    // 检查是否有Text组件，如果没有则添加
                    UnityEngine.UI.Text textComponent = existingCanvas.GetComponentInChildren<UnityEngine.UI.Text>(true);
                    if (textComponent == null)
                    {
                        // 创建Text组件
                        GameObject textObj = new GameObject("DisplayText");
                        textObj.transform.SetParent(existingCanvas.transform);

                        RectTransform rectTransform = textObj.AddComponent<RectTransform>();
                        rectTransform.anchorMin = new Vector2(0, 0);
                        rectTransform.anchorMax = new Vector2(1, 1);
                        rectTransform.offsetMin = Vector2.zero;
                        rectTransform.offsetMax = Vector2.zero;

                        UnityEngine.UI.Text text = textObj.AddComponent<UnityEngine.UI.Text>();
                        text.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
                        text.fontSize = 14;
                        text.alignment = TextAnchor.MiddleCenter;
                        text.color = Color.white;
                        text.text = "设备待机";

                        Debug.Log($"在Canvas '{t.name}' 中添加了Text组件");
                    }
                }
                else
                {
                    // 如果对象本身没有Canvas组件，尝试在子对象中查找
                    Canvas childCanvas = t.GetComponentInChildren<Canvas>(true);
                    if (childCanvas != null)
                    {
                        deviceController.dataDisplays.Add(childCanvas);
                        Debug.Log($"找到数据显示器Canvas(子对象): {childCanvas.name}");
                    }
                    else
                    {
                        // 如果没有找到Canvas组件，自动创建一个
                        GameObject canvasObj = new GameObject("DisplayCanvas");
                        canvasObj.transform.SetParent(t);
                        canvasObj.transform.localPosition = Vector3.zero;
                        canvasObj.transform.localRotation = Quaternion.identity;
                        canvasObj.transform.localScale = new Vector3(0.01f, 0.01f, 0.01f); // 缩小Canvas以适应3D空间

                        // 添加Canvas组件
                        Canvas newCanvas = canvasObj.AddComponent<Canvas>();
                        newCanvas.renderMode = RenderMode.WorldSpace;

                        // 设置Canvas大小与父对象一致
                        RectTransform canvasRectTransform = canvasObj.GetComponent<RectTransform>();
                        canvasRectTransform.sizeDelta = new Vector2(100, 50); // 默认大小

                        // 添加CanvasScaler组件以便更好地控制UI元素
                        UnityEngine.UI.CanvasScaler scaler = canvasObj.AddComponent<UnityEngine.UI.CanvasScaler>();
                        scaler.dynamicPixelsPerUnit = 10;

                        // 添加Text组件
                        GameObject textObj = new GameObject("DisplayText");
                        textObj.transform.SetParent(canvasObj.transform);

                        RectTransform textRectTransform = textObj.AddComponent<RectTransform>();
                        textRectTransform.anchorMin = new Vector2(0, 0);
                        textRectTransform.anchorMax = new Vector2(1, 1);
                        textRectTransform.offsetMin = Vector2.zero;
                        textRectTransform.offsetMax = Vector2.zero;

                        UnityEngine.UI.Text text = textObj.AddComponent<UnityEngine.UI.Text>();
                        text.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
                        text.fontSize = 14;
                        text.alignment = TextAnchor.MiddleCenter;
                        text.color = Color.white;
                        text.text = "设备待机";

                        deviceController.dataDisplays.Add(newCanvas);
                        Debug.Log($"在 {t.name} 上创建了新的数据显示器Canvas");
                    }
                }
            }
        }

        // 更新数据显示器内容
        deviceController.UpdateDataDisplayText();
    }

    /// <summary>
    /// 查找可展开组件（所有叶子节点）
    /// </summary>
    private void FindExpandableComponents()
    {
        deviceController.deviceComponents.Clear();

        // 首先查找所有带有ComponentIdentifier组件的可展开组件
        ComponentIdentifier[] identifiers = deviceController.GetComponentsInChildren<ComponentIdentifier>(true);
        foreach (ComponentIdentifier id in identifiers)
        {
            if (id.componentType == ComponentIdentifier.ComponentType.ExpandableComponent)
            {
                deviceController.deviceComponents.Add(id.transform);
                Debug.Log($"找到标记的可展开组件: {id.name}");
            }
        }

        // 查找所有叶子节点（没有子对象的Transform）
        Transform[] allTransforms = deviceController.GetComponentsInChildren<Transform>(true);
        int leafCount = 0;

        foreach (Transform t in allTransforms)
        {
            // 跳过已经添加的组件
            if (deviceController.deviceComponents.Contains(t))
                continue;

            // 检查是否为叶子节点（没有子对象）
            if (t.childCount == 0)
            {
                deviceController.deviceComponents.Add(t);
                leafCount++;
            }
        }

        Debug.Log($"找到 {leafCount} 个叶子节点组件");
    }
}