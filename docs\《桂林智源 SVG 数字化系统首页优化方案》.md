# **一、优化背景与目标**

为满足白云电气 SVG 设备上云项目需求，实现基于 3D 模型的设备远程监控及无人值守场景应用，针对 SVG 首页界面进行布局与功能优化，聚焦设备关键数据展示、用户操作体验及系统交互逻辑，确保 8 月项目现场上线需求。&#x20;


## **二、页面整体布局规划**

### **（一）布局框架**

采用 “顶栏项目信息 + 左中右三栏内容区” 结构，沿用现有界面风格与布局逻辑：&#x20;


**• 顶栏区域**：包含项目标识、系统信息（区域 1 - 3）&#x20;


**• 内容区**：左中右三栏分布（区域 4 - 6），分别展示系统状态、3D 模型与故障信息、水冷系统数据&#x20;


## **三、各区域优化细节**

### **（一）顶栏区域（区域 1 - 3） **

#### **1. 区域 1：项目标识 **

**• 修改内容**：


◦ 将 Logo 替换为 “桂林智源网站 logo”&#x20;


◦ 名称更新为 “桂林智源 SVG 数字化系统”&#x20;


**• 展示位置**：顶栏左侧


#### **2. 区域 2：功能栏 **

**• 修改内容**：移除实时时间，改成放置“总览视角”、“自动漫游”、“设备展开”等功能按钮&#x20;


**• 展示位置**：顶栏中间


#### **3. 区域 3：系统信息 **

**• 展示内容**：


◦ 实时时间（例：2025/06/23 10:28:52）&#x20;


◦ 设备连接状态（例：连接状态正常）


**• 展示位置**：顶栏右侧


### **（二）左栏区域（区域 4：系统状态与参数） **

#### **1. 系统运行状态 **

**• 展示内容**：


◦ 状态选项：就绪、故障、充电、合高压等待、运行（参考《说明书-用户.docx》）&#x20;


◦ 状态标识：采用颜色编码（如绿色 - 运行中，红色 - 故障）&#x20;


**• 交互功能**：点击状态可查看详细日志


#### **2. 系统关键参数 **

**• 参数列表**：


◦ SVG 总电流&#x20;


◦ SVG 总电压&#x20;


◦ 功率因数


**• 交互功能**：点击参数名称进入详情页，展示趋势曲线与历史数据


#### **3. 系统拓扑图 **

**• 当前方案**：采用静态拓扑图（例：10kV 母线、QS、TS 等元件示意图） ————「SVG系统拓扑图.png」


**• 未来规划**：后期开发动态编辑功能，支持实时拓扑更新


### **（三）中栏区域（区域 5：3D 模型与故障信息） **

#### **1. 3D 设备模型演示 **

#### **2. 故障信息展示 **

**• 展示位置**：3D 模型下方区域&#x20;


**• 展示形式**：列表式呈现，包含字段：


◦ 序号


◦ 日期（例：02/14/25）&#x20;


◦ 时间（例：16:14:30）&#x20;


◦ 事件信息（例：通信错误）


• 字体颜色标识：


▪ 报警：黄色


▪ 故障：红色


▪ 正常：绿色


**• 交互功能**：支持筛选 “所有事件”“实时记录”“报警事件”&#x20;


### **（四）右栏区域（区域 6：水冷系统） **

#### **1. 水冷系统状态 **

**• 运行参数**：


◦ 运作状态（运行 / 停止）&#x20;


◦ 进水压力


◦ 进水流量


◦ 进水温度


◦ 出水压力


◦ 出水温度


**• 交互功能**：点击进入水冷系统详情页，查看实时数据曲线


#### **2. 水冷拓扑图 **

**• 当前方案**：采用静态拓扑图展示水循环路径与关键部件——「SVG系统拓扑图.png」


**• 未来规划**：后期实现动态拓扑编辑，关联设备运行状态
