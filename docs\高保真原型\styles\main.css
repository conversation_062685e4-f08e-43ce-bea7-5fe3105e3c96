/* 全局样式和基础设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 科技蓝色主题色彩 */
    --primary-color: #00d4ff;
    --secondary-color: #0099cc;
    --accent-color: #66e0ff;
    --success-color: #00ff88;
    --warning-color: #ffaa00;
    --error-color: #ff4444;
    
    /* 背景色 */
    --bg-primary: #0a0e1a;
    --bg-secondary: #1a1f2e;
    --bg-tertiary: #2a3142;
    --bg-card: rgba(26, 31, 46, 0.8);
    --bg-glass: rgba(0, 212, 255, 0.1);
    
    /* 文字颜色 */
    --text-primary: #ffffff;
    --text-secondary: #b8c5d6;
    --text-muted: #7a8ba0;
    
    /* 边框和阴影 */
    --border-color: rgba(0, 212, 255, 0.3);
    --shadow-primary: 0 4px 20px rgba(0, 212, 255, 0.2);
    --shadow-secondary: 0 2px 10px rgba(0, 0, 0, 0.3);
    
    /* 字体 */
    --font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
    --font-mono: 'Consolas', 'Monaco', monospace;
}

body {
    font-family: var(--font-family);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    color: var(--text-primary);
    overflow: hidden;
    height: 100vh;
    position: relative;
}

/* 科技感背景动画 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(102, 224, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* 主应用容器 */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
}

/* 顶部导航栏 */
.header {
    height: 70px;
    background: linear-gradient(90deg, 
        rgba(26, 31, 46, 0.95) 0%, 
        rgba(42, 49, 66, 0.95) 50%, 
        rgba(26, 31, 46, 0.95) 100%);
    backdrop-filter: blur(10px);
    border-bottom: 2px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    box-shadow: var(--shadow-secondary);
    position: relative;
    z-index: 100;
}

.header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        var(--primary-color) 50%, 
        transparent 100%);
    animation: headerGlow 3s ease-in-out infinite alternate;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 30px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
}

.logo i {
    font-size: 28px;
    animation: logoSpin 4s linear infinite;
}

.logo-text {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.system-title {
    font-size: 20px;
    font-weight: 500;
    color: var(--text-primary);
    letter-spacing: 1px;
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.time-display {
    font-family: var(--font-mono);
    font-size: 18px;
    color: var(--accent-color);
    background: var(--bg-glass);
    padding: 8px 20px;
    border-radius: 20px;
    border: 1px solid var(--border-color);
    backdrop-filter: blur(5px);
}

.header-right {
    display: flex;
    align-items: center;
}

.status-indicators {
    display: flex;
    gap: 20px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--text-secondary);
}

.status-item.online {
    color: var(--success-color);
}

.status-item i {
    font-size: 12px;
}

.status-item.online i {
    animation: pulse 2s ease-in-out infinite;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 300px 1fr 350px;
    gap: 20px;
    padding: 20px;
    overflow: hidden;
}

/* 左侧控制面板 */
.left-panel {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-primary);
    overflow-y: auto;
}

.panel-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, 
        rgba(0, 212, 255, 0.1) 0%, 
        rgba(0, 153, 204, 0.05) 100%);
}

.panel-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.panel-header h3::before {
    content: '';
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, var(--primary-color), var(--accent-color));
    border-radius: 2px;
}

.control-section {
    padding: 20px;
}

.control-group {
    margin-bottom: 30px;
}

.control-group h4 {
    font-size: 16px;
    color: var(--text-primary);
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.control-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.control-btn {
    background: linear-gradient(135deg, 
        rgba(0, 212, 255, 0.1) 0%, 
        rgba(0, 153, 204, 0.1) 100%);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    position: relative;
    overflow: hidden;
}

.control-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(0, 212, 255, 0.2) 50%, 
        transparent 100%);
    transition: left 0.5s ease;
}

.control-btn:hover::before {
    left: 100%;
}

.control-btn:hover {
    background: linear-gradient(135deg, 
        rgba(0, 212, 255, 0.2) 0%, 
        rgba(0, 153, 204, 0.2) 100%);
    border-color: var(--primary-color);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
    transform: translateY(-2px);
}

.control-btn.primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--bg-primary);
    font-weight: 600;
}

.control-btn.primary:hover {
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
    box-shadow: 0 0 25px rgba(0, 212, 255, 0.5);
}

.control-btn i {
    font-size: 16px;
}

/* 设备状态列表 */
.device-status-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.device-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: rgba(0, 212, 255, 0.05);
    border: 1px solid rgba(0, 212, 255, 0.1);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.device-item:hover {
    background: rgba(0, 212, 255, 0.1);
    border-color: rgba(0, 212, 255, 0.3);
    transform: translateX(5px);
}

.device-name {
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
}

.device-status {
    font-size: 12px;
    padding: 4px 12px;
    border-radius: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.device-status.running {
    background: rgba(0, 255, 136, 0.2);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.device-status.warning {
    background: rgba(255, 170, 0, 0.2);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
    animation: warningBlink 2s ease-in-out infinite;
}

.device-status.error {
    background: rgba(255, 68, 68, 0.2);
    color: var(--error-color);
    border: 1px solid var(--error-color);
}

/* 中央3D展示区域 */
.center-panel {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-primary);
    position: relative;
    overflow: hidden;
}

.unity-container {
    width: 100%;
    height: 100%;
    position: relative;
}

.unity-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, 
        rgba(0, 212, 255, 0.05) 0%, 
        rgba(0, 153, 204, 0.05) 100%);
}

.placeholder-content {
    text-align: center;
    color: var(--text-secondary);
}

.placeholder-content i {
    font-size: 64px;
    color: var(--primary-color);
    margin-bottom: 20px;
    display: block;
}

.placeholder-content h3 {
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.placeholder-content p {
    font-size: 16px;
    margin-bottom: 30px;
}

.loading-bar {
    width: 300px;
    height: 4px;
    background: rgba(0, 212, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin: 0 auto;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 2px;
    width: 0%;
    animation: loadingProgress 3s ease-in-out infinite;
}

/* 3D场景工具栏 */
.scene-toolbar {
    position: absolute;
    top: 20px;
    right: 20px;
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 8px;
    display: flex;
    gap: 5px;
}

.toolbar-group {
    display: flex;
    gap: 5px;
}

.toolbar-btn {
    width: 40px;
    height: 40px;
    background: transparent;
    border: 1px solid transparent;
    color: var(--text-secondary);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toolbar-btn:hover {
    background: rgba(0, 212, 255, 0.2);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 1600px) {
    .main-content {
        grid-template-columns: 280px 1fr 320px;
    }
}

@media (max-width: 1400px) {
    .main-content {
        grid-template-columns: 260px 1fr 300px;
    }
    
    .header {
        padding: 0 20px;
    }
    
    .system-title {
        font-size: 18px;
    }
}

@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
    }
    
    .left-panel,
    .right-panel {
        height: 200px;
    }
}
