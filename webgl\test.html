<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>测试页面 - 验证修复</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #ccc;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>页面重构测试</h1>
        
        <div class="test-item info">
            <h3>测试说明</h3>
            <p>这个页面用于测试重构后的页面结构是否正常工作。</p>
            <ul>
                <li><strong>index.html</strong> - 只包含Unity WebGL内容</li>
                <li><strong>main.html</strong> - 包含完整的页面布局和功能</li>
                <li><strong>通信机制</strong> - 通过postMessage实现页面间通信</li>
            </ul>
        </div>
        
        <div class="test-item">
            <h3>测试链接</h3>
            <button onclick="window.open('index.html', '_blank')">打开 Unity 页面 (index.html)</button>
            <button onclick="window.open('main.html', '_blank')">打开 主页面 (main.html)</button>
        </div>
        
        <div class="test-item">
            <h3>主页面预览</h3>
            <iframe src="main.html" title="主页面预览"></iframe>
        </div>
        
        <div class="test-item success">
            <h3>修复内容</h3>
            <ul>
                <li>✅ 移除了postMessage中的Unity实例对象传递</li>
                <li>✅ 添加了消息来源验证</li>
                <li>✅ 增强了错误处理机制</li>
                <li>✅ 添加了调试日志输出</li>
            </ul>
        </div>
        
        <div class="test-item">
            <h3>文件结构</h3>
            <pre>
webgl/
├── index.html          # Unity专用页面（可被Unity覆盖）
├── main.html           # 主页面（包含所有业务功能）
├── styles.css          # 样式文件
├── charts.js           # 图表配置
├── main.js             # 工具函数
└── test.html           # 测试页面（当前页面）
            </pre>
        </div>
    </div>
    
    <script>
        // 检查控制台是否有错误
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e.error);
        });
        
        // 监听postMessage错误
        window.addEventListener('message', function(event) {
            console.log('收到消息:', event.data);
        });
        
        console.log('测试页面加载完成');
    </script>
</body>
</html>
