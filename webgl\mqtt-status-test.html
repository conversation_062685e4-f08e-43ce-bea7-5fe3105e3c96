<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MQTT 状态显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .status-indicator {
            padding: 8px 16px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .connected {
            background-color: #4caf50;
            color: white;
        }
        
        .disconnected {
            background-color: #f44336;
            color: white;
        }
        
        .data-quality-indicator {
            padding: 8px 16px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .excellent { background-color: #4caf50; color: white; }
        .good { background-color: #2196f3; color: white; }
        .fair { background-color: #ff9800; color: white; }
        .poor { background-color: #f44336; color: white; }
        
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background-color: #2196f3;
            color: white;
            cursor: pointer;
        }
        
        button:hover {
            background-color: #1976d2;
        }
        
        .log {
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>MQTT 双系统状态显示测试</h1>
    
    <div class="test-container">
        <h2>模拟状态显示</h2>
        <div id="mqtt-status" class="status-indicator disconnected">MQTT 未连接</div>
        <div id="data-quality-indicator" class="data-quality-indicator poor">数据质量: 0%</div>
        
        <div>
            <button onclick="simulateElectricalConnected()">模拟电气系统连接</button>
            <button onclick="simulateCoolingConnected()">模拟水冷系统连接</button>
            <button onclick="simulateBothConnected()">模拟双系统连接</button>
            <button onclick="simulateDisconnected()">模拟断开连接</button>
        </div>
    </div>
    
    <div class="test-container">
        <h2>实际状态测试</h2>
        <div>
            <button onclick="testRealStatus()">获取实际 MQTT 状态</button>
            <button onclick="testDebugInfo()">获取调试信息</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        <div id="log" class="log">点击按钮开始测试...</div>
    </div>

    <script>
        // 模拟 getMQTTConnectionStatus 函数的不同返回值
        function simulateElectricalConnected() {
            const mockStatus = {
                electrical: {
                    isConnected: true,
                    reconnectAttempts: 0,
                    dataProcessing: { successRate: 85.5 }
                },
                cooling: {
                    isConnected: false,
                    reconnectAttempts: 2,
                    dataProcessing: { successRate: 0 }
                },
                overall: {
                    isConnected: true,
                    connectedSystems: 1,
                    totalSystems: 2
                }
            };
            
            updateMQTTStatusDisplay(mockStatus);
            log('模拟电气系统连接状态');
        }
        
        function simulateCoolingConnected() {
            const mockStatus = {
                electrical: {
                    isConnected: false,
                    reconnectAttempts: 1,
                    dataProcessing: { successRate: 0 }
                },
                cooling: {
                    isConnected: true,
                    reconnectAttempts: 0,
                    dataProcessing: { successRate: 92.3 }
                },
                overall: {
                    isConnected: true,
                    connectedSystems: 1,
                    totalSystems: 2
                }
            };
            
            updateMQTTStatusDisplay(mockStatus);
            log('模拟水冷系统连接状态');
        }
        
        function simulateBothConnected() {
            const mockStatus = {
                electrical: {
                    isConnected: true,
                    reconnectAttempts: 0,
                    dataProcessing: { successRate: 96.8 }
                },
                cooling: {
                    isConnected: true,
                    reconnectAttempts: 0,
                    dataProcessing: { successRate: 88.2 }
                },
                overall: {
                    isConnected: true,
                    connectedSystems: 2,
                    totalSystems: 2
                }
            };
            
            updateMQTTStatusDisplay(mockStatus);
            log('模拟双系统连接状态');
        }
        
        function simulateDisconnected() {
            const mockStatus = {
                electrical: {
                    isConnected: false,
                    reconnectAttempts: 3,
                    dataProcessing: { successRate: 0 }
                },
                cooling: {
                    isConnected: false,
                    reconnectAttempts: 1,
                    dataProcessing: { successRate: 0 }
                },
                overall: {
                    isConnected: false,
                    connectedSystems: 0,
                    totalSystems: 2
                }
            };
            
            updateMQTTStatusDisplay(mockStatus);
            log('模拟断开连接状态');
        }
        
        function updateMQTTStatusDisplay(status) {
            // 更新头部的 MQTT 状态指示器
            const mqttStatusElement = document.getElementById('mqtt-status');
            if (mqttStatusElement) {
                if (status.overall && status.overall.isConnected) {
                    mqttStatusElement.className = 'status-indicator connected';
                    mqttStatusElement.textContent = `MQTT 已连接 (${status.overall.connectedSystems}/${status.overall.totalSystems})`;
                } else {
                    mqttStatusElement.className = 'status-indicator disconnected';
                    
                    // 获取重连次数（优先显示电气系统的重连次数）
                    let reconnectAttempts = 0;
                    if (status.electrical && status.electrical.reconnectAttempts !== undefined) {
                        reconnectAttempts = status.electrical.reconnectAttempts;
                    } else if (status.cooling && status.cooling.reconnectAttempts !== undefined) {
                        reconnectAttempts = status.cooling.reconnectAttempts;
                    }
                    
                    mqttStatusElement.textContent = `MQTT 未连接 (重试: ${reconnectAttempts})`;
                }
            }

            // 更新数据质量指示器（优先显示电气系统的数据质量）
            const qualityElement = document.getElementById('data-quality-indicator');
            if (qualityElement) {
                let successRate = 0;
                let hasData = false;
                
                // 优先使用电气系统的数据质量
                if (status.electrical && status.electrical.dataProcessing) {
                    successRate = status.electrical.dataProcessing.successRate || 0;
                    hasData = true;
                } else if (status.cooling && status.cooling.dataProcessing) {
                    successRate = status.cooling.dataProcessing.successRate || 0;
                    hasData = true;
                }
                
                if (hasData) {
                    qualityElement.textContent = `数据质量: ${successRate.toFixed(1)}%`;
                    
                    // 根据成功率设置样式
                    qualityElement.className = 'data-quality-indicator';
                    if (successRate >= 95) {
                        qualityElement.classList.add('excellent');
                    } else if (successRate >= 80) {
                        qualityElement.classList.add('good');
                    } else if (successRate >= 60) {
                        qualityElement.classList.add('fair');
                    } else {
                        qualityElement.classList.add('poor');
                    }
                } else {
                    qualityElement.textContent = '数据质量: 0%';
                    qualityElement.className = 'data-quality-indicator poor';
                }
            }
        }
        
        function testRealStatus() {
            try {
                if (typeof getMQTTConnectionStatus === 'function') {
                    const status = getMQTTConnectionStatus();
                    log('实际 MQTT 状态: ' + JSON.stringify(status, null, 2));
                    updateMQTTStatusDisplay(status);
                } else {
                    log('错误: getMQTTConnectionStatus 函数不存在（需要在 main.html 中测试）');
                }
            } catch (error) {
                log('获取实际状态时出错: ' + error.message);
            }
        }
        
        function testDebugInfo() {
            try {
                if (typeof getMQTTDebugInfo === 'function') {
                    const debugInfo = getMQTTDebugInfo();
                    log('MQTT 调试信息: ' + JSON.stringify(debugInfo, null, 2));
                } else {
                    log('错误: getMQTTDebugInfo 函数不存在（需要在 main.html 中测试）');
                }
            } catch (error) {
                log('获取调试信息时出错: ' + error.message);
            }
        }
        
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
        }
        
        // 初始化显示
        simulateDisconnected();
    </script>
</body>
</html>
