/**
 * 数据处理功能测试脚本
 * 用于验证 ElectricalDataProcessor 是否能正确处理实际的 MQTT 数据格式
 */

// 模拟实际接收到的 MQTT 数据
const sampleMQTTData = {
    "message": [
        {"id":"HMI_32040","name":"SVG电流la","ts":"2025-07-23 15:09:56.058","value":"0"},
        {"id":"HMI_32044","name":"SVG电流lc","ts":"2025-07-23 15:09:56.058","value":"1"},
        {"id":"HMI_30039_9","name":"备用","ts":"2025-07-23 15:09:56.058","value":"0"},
        {"id":"HMI_32042","name":"SVG电流lb","ts":"2025-07-23 15:09:56.058","value":"1"},
        {"id":"HMI_30039_11","name":"故障2","ts":"2025-07-23 15:09:56.058","value":"0"},
        {"id":"HMI_30039_3","name":"复位","ts":"2025-07-23 15:09:56.058","value":"0"},
        {"id":"HMI_30039_7","name":"高压","ts":"2025-07-23 15:09:56.058","value":"0"},
        {"id":"HMI_30039_4","name":"就绪","ts":"2025-07-23 15:09:56.058","value":"0"},
        {"id":"HMI_32032","name":"母线电压Ubc","ts":"2025-07-23 15:09:56.058","value":"0.1"},
        {"id":"HMI_32034","name":"母线电压Uca","ts":"2025-07-23 15:09:56.058","value":"0"},
        {"id":"HMI_30039_0","name":"起始状态","ts":"2025-07-23 15:09:56.058","value":"0"},
        {"id":"HMI_32030","name":"母线电压Uab","ts":"2025-07-23 15:09:56.058","value":"0"},
        {"id":"HMI_30039_2","name":"单元自检","ts":"2025-07-23 15:09:56.058","value":"0"},
        {"id":"HMI_32050","name":"功率因数","ts":"2025-07-23 15:09:56.058","value":"0.31"},
        {"id":"HMI_30039_8","name":"开机自检","ts":"2025-07-23 15:09:56.058","value":"0"},
        {"id":"HMI_30039_12","name":"复位2","ts":"2025-07-23 15:09:56.058","value":"0"},
        {"id":"HMI_32046","name":"网侧负载无功电流","ts":"2025-07-23 15:09:56.058","value":"0"},
        {"id":"HMI_32048","name":"负载无功功率","ts":"2025-07-23 15:09:56.058","value":"0"},
        {"id":"HMI_30039_1","name":"充电","ts":"2025-07-23 15:09:56.058","value":"0"},
        {"id":"HMI_30039_5","name":"运行","ts":"2025-07-23 15:09:56.058","value":"0"},
        {"id":"HMI_30039_6","name":"故障","ts":"2025-07-23 15:09:56.058","value":"0"},
        {"id":"HMI_30039_10","name":"合高压等待","ts":"2025-07-23 15:09:56.058","value":"1"}
    ],
    "sources": "[{\"id\":\"HMI_32040\",\"value\":0},{\"id\":\"HMI_32044\",\"value\":1},{\"id\":\"HMI_30039_9\",\"value\":0},{\"id\":\"HMI_32042\",\"value\":1},{\"id\":\"HMI_30039_11\",\"value\":0},{\"id\":\"HMI_30039_3\",\"value\":0},{\"id\":\"HMI_30039_7\",\"value\":0},{\"id\":\"HMI_30039_4\",\"value\":0},{\"id\":\"HMI_32032\",\"value\":0.1},{\"id\":\"HMI_32034\",\"value\":0},{\"id\":\"HMI_30039_0\",\"value\":0},{\"id\":\"HMI_32030\",\"value\":0},{\"id\":\"HMI_30039_2\",\"value\":0},{\"id\":\"HMI_32050\",\"value\":0.31},{\"id\":\"HMI_30039_8\",\"value\":0},{\"id\":\"HMI_30039_12\",\"value\":0},{\"id\":\"HMI_32046\",\"value\":-0},{\"id\":\"HMI_32048\",\"value\":-0},{\"id\":\"HMI_30039_1\",\"value\":0},{\"id\":\"HMI_30039_5\",\"value\":0},{\"id\":\"HMI_30039_6\",\"value\":0},{\"id\":\"HMI_30039_10\",\"value\":1}]"
};

/**
 * 测试数据处理功能
 */
function testDataProcessing() {
    console.log('=== 开始测试数据处理功能 ===');
    
    // 检查 ElectricalDataProcessor 是否可用
    if (typeof ElectricalDataProcessor === 'undefined') {
        console.error('ElectricalDataProcessor 未定义，请确保已加载 electricalDataProcessor.js');
        return;
    }
    
    // 创建数据处理器实例
    const processor = new ElectricalDataProcessor();
    console.log('✓ 数据处理器创建成功');
    
    // 测试数据处理
    console.log('\n--- 测试实际 MQTT 数据处理 ---');
    const result = processor.processData(sampleMQTTData);
    
    if (result.success) {
        console.log('✓ 数据处理成功');
        console.log('处理后的数据:', result.data);
        console.log('数据质量:', result.metadata.dataQuality);
        console.log('完整性检查:', result.metadata.integrityCheck);
        
        // 显示处理后的参数
        console.log('\n--- 处理后的参数列表 ---');
        Object.keys(result.data.properties).forEach(id => {
            const prop = result.data.properties[id];
            console.log(`${prop.name} (${id}): ${prop.value} ${prop.unit} [${prop.isValid ? '有效' : '无效'}]`);
        });
        
    } else {
        console.error('✗ 数据处理失败:', result.error);
        if (result.details) {
            console.error('错误详情:', result.details);
        }
    }
    
    // 测试统计信息
    console.log('\n--- 处理统计信息 ---');
    const stats = processor.getStatistics();
    console.log('统计信息:', stats);
    
    // 测试支持的参数列表
    console.log('\n--- 支持的参数列表 ---');
    const supportedParams = processor.getSupportedProperties();
    supportedParams.forEach(param => {
        console.log(`${param.name} (${param.id}): ${param.type} ${param.unit}`);
    });
    
    console.log('\n=== 数据处理功能测试完成 ===');
    
    return result;
}

/**
 * 测试多种数据格式
 */
function testMultipleFormats() {
    console.log('\n=== 测试多种数据格式 ===');
    
    const processor = new ElectricalDataProcessor();
    
    // 测试格式1：新的消息格式
    console.log('\n--- 测试新消息格式 ---');
    const newFormatResult = processor.processData(sampleMQTTData);
    console.log('新格式处理结果:', newFormatResult.success ? '成功' : '失败');
    
    // 测试格式2：旧的属性格式
    console.log('\n--- 测试旧属性格式 ---');
    const oldFormatData = {
        properties: {
            'HMI_30039_5': 1,
            'HMI_30039_6': 0,
            'HMI_32050': 0.95
        },
        timestamp: new Date().toISOString(),
        deviceId: 'test-device'
    };
    
    const oldFormatResult = processor.processData(oldFormatData);
    console.log('旧格式处理结果:', oldFormatResult.success ? '成功' : '失败');
    
    // 测试格式3：无效格式
    console.log('\n--- 测试无效格式 ---');
    const invalidFormatData = {
        invalidField: 'test'
    };
    
    const invalidFormatResult = processor.processData(invalidFormatData);
    console.log('无效格式处理结果:', invalidFormatResult.success ? '成功' : '失败');
    if (!invalidFormatResult.success) {
        console.log('预期的错误:', invalidFormatResult.error);
    }
    
    console.log('\n=== 多格式测试完成 ===');
}

/**
 * 性能测试
 */
function performanceTest() {
    console.log('\n=== 性能测试 ===');
    
    const processor = new ElectricalDataProcessor();
    const iterations = 1000;
    
    console.log(`开始处理 ${iterations} 次数据...`);
    const startTime = performance.now();
    
    for (let i = 0; i < iterations; i++) {
        processor.processData(sampleMQTTData);
    }
    
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    const avgTime = totalTime / iterations;
    
    console.log(`总耗时: ${totalTime.toFixed(2)} ms`);
    console.log(`平均耗时: ${avgTime.toFixed(4)} ms/次`);
    console.log(`处理速度: ${(1000 / avgTime).toFixed(0)} 次/秒`);
    
    const stats = processor.getStatistics();
    console.log(`成功率: ${stats.successRate.toFixed(2)}%`);
    
    console.log('\n=== 性能测试完成 ===');
}

/**
 * 运行所有测试
 */
function runAllTests() {
    try {
        testDataProcessing();
        testMultipleFormats();
        performanceTest();
        
        console.log('\n🎉 所有测试完成！');
        
        // 返回测试结果供外部使用
        return {
            success: true,
            message: '所有测试通过'
        };
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// 如果在浏览器环境中，添加到全局对象
if (typeof window !== 'undefined') {
    window.testDataProcessing = testDataProcessing;
    window.testMultipleFormats = testMultipleFormats;
    window.performanceTest = performanceTest;
    window.runAllTests = runAllTests;
    
    // 页面加载完成后自动运行测试
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            console.log('页面加载完成，可以运行以下测试命令：');
            console.log('testDataProcessing() - 测试数据处理功能');
            console.log('testMultipleFormats() - 测试多种数据格式');
            console.log('performanceTest() - 性能测试');
            console.log('runAllTests() - 运行所有测试');
        });
    } else {
        console.log('测试脚本已加载，可以运行测试命令');
    }
}

// Node.js 环境支持
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testDataProcessing,
        testMultipleFormats,
        performanceTest,
        runAllTests,
        sampleMQTTData
    };
}
