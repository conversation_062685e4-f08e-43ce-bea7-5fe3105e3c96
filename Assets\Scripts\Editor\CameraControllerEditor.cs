using UnityEngine;
using UnityEditor;
using UnityEditor.SceneManagement;

/// <summary>
/// 相机位置编辑器扩展，用于在编辑器中管理CameraController的位置设置
/// </summary>
[CustomEditor(typeof(CameraController))]
public class CameraPositionEditor : Editor
{
    private SerializedProperty firstCameraInitialPosition;
    private SerializedProperty deviceCameraPositions;
    private SerializedProperty positionTransitionSpeed;
    private SerializedProperty rotationTransitionSpeed;
    private SerializedProperty fovTransitionSpeed;
    private SerializedProperty deviceViewDwellTime;
    private SerializedProperty controlledCanvases;
    
    // 其他属性
    private SerializedProperty moveSpeed;
    private SerializedProperty sprintMultiplier;
    private SerializedProperty horizontalRotationSpeed;
    private SerializedProperty verticalRotationSpeed;
    private SerializedProperty minVerticalAngle;
    private SerializedProperty maxVerticalAngle;
    private SerializedProperty zoomSpeed;
    private SerializedProperty minFieldOfView;
    private SerializedProperty maxFieldOfView;
    private SerializedProperty panSpeed;
    private SerializedProperty deviceController;
    
    private bool showPositionSettings = true;
    private bool showMoveSettings = true;
    private bool showRotationSettings = true;
    private bool showZoomSettings = true;
    
    private void OnEnable()
    {
        // 位置管理属性
        firstCameraInitialPosition = serializedObject.FindProperty("firstCameraInitialPosition");
        deviceCameraPositions = serializedObject.FindProperty("deviceCameraPositions");
        positionTransitionSpeed = serializedObject.FindProperty("positionTransitionSpeed");
        rotationTransitionSpeed = serializedObject.FindProperty("rotationTransitionSpeed");
        fovTransitionSpeed = serializedObject.FindProperty("fovTransitionSpeed");
        deviceViewDwellTime = serializedObject.FindProperty("deviceViewDwellTime");
        controlledCanvases = serializedObject.FindProperty("controlledCanvases");
        
        // 其他属性
        moveSpeed = serializedObject.FindProperty("moveSpeed");
        sprintMultiplier = serializedObject.FindProperty("sprintMultiplier");
        horizontalRotationSpeed = serializedObject.FindProperty("horizontalRotationSpeed");
        verticalRotationSpeed = serializedObject.FindProperty("verticalRotationSpeed");
        minVerticalAngle = serializedObject.FindProperty("minVerticalAngle");
        maxVerticalAngle = serializedObject.FindProperty("maxVerticalAngle");
        zoomSpeed = serializedObject.FindProperty("zoomSpeed");
        minFieldOfView = serializedObject.FindProperty("minFieldOfView");
        maxFieldOfView = serializedObject.FindProperty("maxFieldOfView");
        panSpeed = serializedObject.FindProperty("panSpeed");
        deviceController = serializedObject.FindProperty("deviceController");
    }
    
    public override void OnInspectorGUI()
    {
        serializedObject.Update();
        
        CameraController cameraController = (CameraController)target;

        // UI设置
        EditorGUILayout.PropertyField(controlledCanvases, new GUIContent("控制的Canvas列表", "需要在漫游时控制显示/隐藏的Canvas列表"), true);
        EditorGUILayout.PropertyField(deviceController, new GUIContent("设备控制器", "设备控制器引用，用于控制设备开关"));
            
        
        // 位置管理设置
        EditorGUILayout.Space();
        showPositionSettings = EditorGUILayout.Foldout(showPositionSettings, "漫游位置管理设置", true);
        if (showPositionSettings)
        {
            EditorGUI.indentLevel++;
            
            EditorGUILayout.PropertyField(firstCameraInitialPosition, new GUIContent("总览初始位置", "用于总览的初始化位置，F1键切换到该位置"));
            
            // 设备视角位置列表
            EditorGUILayout.PropertyField(deviceCameraPositions, new GUIContent("设备视角位置列表", "用于设备的3D视角展示，F2键开始和终止漫游"), true);
            
            EditorGUILayout.PropertyField(positionTransitionSpeed, new GUIContent("位置切换速度", "相机位置切换的速度"));
            EditorGUILayout.PropertyField(rotationTransitionSpeed, new GUIContent("旋转切换速度", "相机旋转切换的速度"));
            EditorGUILayout.PropertyField(fovTransitionSpeed, new GUIContent("视野切换速度", "相机视野切换的速度"));
            EditorGUILayout.PropertyField(deviceViewDwellTime, new GUIContent("视角停留时间", "设备视角漫游时在每个位置停留的时间(秒)"));
            
            
            EditorGUILayout.Space();
            
            // 添加当前相机位置按钮
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("设置为总览初始位置"))
            {
                SetFirstCameraInitialPosition(cameraController);
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("添加当前位置到设备视角列表"))
            {
                AddCurrentPositionToDeviceViews(cameraController);
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUI.indentLevel--;
        }
        
        // 移动设置
        EditorGUILayout.Space();
        showMoveSettings = EditorGUILayout.Foldout(showMoveSettings, "移动设置", true);
        if (showMoveSettings)
        {
            EditorGUI.indentLevel++;
            
            EditorGUILayout.PropertyField(moveSpeed);
            EditorGUILayout.PropertyField(sprintMultiplier);
            EditorGUILayout.PropertyField(panSpeed);
            
            EditorGUI.indentLevel--;
        }
        
        // 旋转设置
        EditorGUILayout.Space();
        showRotationSettings = EditorGUILayout.Foldout(showRotationSettings, "旋转设置", true);
        if (showRotationSettings)
        {
            EditorGUI.indentLevel++;
            
            EditorGUILayout.PropertyField(horizontalRotationSpeed);
            EditorGUILayout.PropertyField(verticalRotationSpeed);
            EditorGUILayout.PropertyField(minVerticalAngle);
            EditorGUILayout.PropertyField(maxVerticalAngle);
            
            EditorGUI.indentLevel--;
        }
        
        // 缩放设置
        EditorGUILayout.Space();
        showZoomSettings = EditorGUILayout.Foldout(showZoomSettings, "缩放设置", true);
        if (showZoomSettings)
        {
            EditorGUI.indentLevel++;
            
            EditorGUILayout.PropertyField(zoomSpeed);
            EditorGUILayout.PropertyField(minFieldOfView);
            EditorGUILayout.PropertyField(maxFieldOfView);
            
            EditorGUI.indentLevel--;
        }
        
        serializedObject.ApplyModifiedProperties();
    }
    
    /// <summary>
    /// 设置当前相机位置为总览初始位置
    /// </summary>
    private void SetFirstCameraInitialPosition(CameraController cameraController)
    {
        // 创建一个新的GameObject作为位置标记
        GameObject positionMarker = new GameObject("FirstCameraPosition");
        positionMarker.transform.position = cameraController.transform.position;
        positionMarker.transform.rotation = cameraController.transform.rotation;
        
        // 添加相机组件以保存视野信息
        Camera camera = positionMarker.AddComponent<Camera>();
        camera.fieldOfView = cameraController.GetComponent<Camera>().fieldOfView;
        camera.enabled = false; // 禁用该相机，仅用于存储信息
        
        // 设置为总览初始位置
        firstCameraInitialPosition.objectReferenceValue = positionMarker.transform;
        serializedObject.ApplyModifiedProperties();
        
        // 将位置标记设置为相机的子物体，便于管理
        positionMarker.transform.parent = cameraController.transform.parent;
        
        // 标记场景已修改
        EditorUtility.SetDirty(target);
        EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
    }
    
    /// <summary>
    /// 添加当前相机位置到设备视角列表
    /// </summary>
    private void AddCurrentPositionToDeviceViews(CameraController cameraController)
    {
        // 创建一个新的GameObject作为位置标记
        GameObject positionMarker = new GameObject("DeviceViewPosition_" + deviceCameraPositions.arraySize);
        positionMarker.transform.position = cameraController.transform.position;
        positionMarker.transform.rotation = cameraController.transform.rotation;
        
        // 添加相机组件以保存视野信息
        Camera camera = positionMarker.AddComponent<Camera>();
        camera.fieldOfView = cameraController.GetComponent<Camera>().fieldOfView;
        camera.enabled = false; // 禁用该相机，仅用于存储信息
        
        // 添加到设备视角列表
        deviceCameraPositions.arraySize++;
        deviceCameraPositions.GetArrayElementAtIndex(deviceCameraPositions.arraySize - 1).objectReferenceValue = positionMarker.transform;
        serializedObject.ApplyModifiedProperties();
        
        // 将位置标记设置为相机的子物体，便于管理
        positionMarker.transform.parent = cameraController.transform.parent;
        
        // 标记场景已修改
        EditorUtility.SetDirty(target);
        EditorSceneManager.MarkSceneDirty(EditorSceneManager.GetActiveScene());
    }
}