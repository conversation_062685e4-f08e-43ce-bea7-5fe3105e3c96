using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// 连线管理器 - 管理3D物体与2D Canvas UI元素之间的连线
/// </summary>
public class LineManager : MonoBehaviour
{
    [System.Serializable]
    public class ConnectionLine
    {
        [Tooltip("连线名称")]
        public string name;
        
        [Tooltip("3D物体引用")]
        public Transform objectTransform;
        
        [Tooltip("UI元素引用")]
        public RectTransform uiElement;
        
        [Tooltip("连线UI对象")]
        public RectTransform lineObject;
        
        [Tooltip("连线图像组件")]
        public Image lineImage;
        
        [Tooltip("连线宽度")]
        public float lineWidth = 2f;
        
        [Tooltip("连线颜色")]
        public Color lineColor = Color.white;
        
        [Tooltip("3D物体连接点偏移")]
        public Vector3 objectOffset = Vector3.zero;
        
        [Tooltip("UI元素连接点偏移")]
        public Vector2 uiOffset = Vector2.zero;
        
        [Tooltip("是否显示连线")]
        public bool isVisible = true;
        
        [Tooltip("当3D物体在相机视野外时是否隐藏连线")]
        public bool hideWhenOutOfView = true;
        
        [Tooltip("当3D物体被遮挡时是否隐藏连线和UI")]
        public bool hideWhenOccluded = true;
        
        [Tooltip("遮挡检测的图层")]
        public LayerMask occlusionLayers = -1;
    }
    
    [Header("连线设置")]
    [Tooltip("连线列表")]
    public List<ConnectionLine> connectionLines = new List<ConnectionLine>();
    
    [Tooltip("Canvas引用")]
    public Canvas targetCanvas;
    
    [Tooltip("相机引用")]
    public Camera mainCamera;
    
    [Tooltip("更新频率 - 每秒更新次数")]
    public float updateFrequency = 30f;
    
    // 私有变量
    private float updateInterval;
    private float lastUpdateTime;
    
    void Start()
    {
        // 初始化
        InitializeLines();
        
        // 设置更新间隔
        updateInterval = 1f / updateFrequency;
        lastUpdateTime = 0f;
        
        // 如果没有设置相机，使用主相机
        if (mainCamera == null)
        {
            mainCamera = Camera.main;
        }
        
        // 如果没有设置Canvas，尝试在场景中查找
        if (targetCanvas == null)
        {
            targetCanvas = FindObjectOfType<Canvas>();
            if (targetCanvas == null)
            {
                Debug.LogError("未找到Canvas组件，请手动设置引用");
            }
        }
    }
    
    void Update()
    {
        // 按照设定的频率更新连线
        if (Time.time - lastUpdateTime >= updateInterval)
        {
            UpdateAllLines();
            lastUpdateTime = Time.time;
        }
    }
    
    /// <summary>
    /// 初始化所有连线
    /// </summary>
    private void InitializeLines()
    {
        foreach (ConnectionLine line in connectionLines)
        {
            if (line.lineObject != null && line.lineImage == null)
            {
                line.lineImage = line.lineObject.GetComponent<Image>();
                if (line.lineImage == null)
                {
                    Debug.LogWarning($"连线 '{line.name}' 的lineObject上没有Image组件");
                }
                else
                {
                    // 设置连线颜色和宽度
                    line.lineImage.color = line.lineColor;
                    line.lineObject.sizeDelta = new Vector2(line.lineObject.sizeDelta.x, line.lineWidth);
                }
            }
        }
    }
    
    /// <summary>
    /// 更新所有连线
    /// </summary>
    private void UpdateAllLines()
    {
        foreach (ConnectionLine line in connectionLines)
        {
            if (line.objectTransform == null || line.uiElement == null || line.lineObject == null)
            {
                continue;
            }
            
            UpdateLine(line);
        }
    }
    
    /// <summary>
    /// 更新单条连线
    /// </summary>
    private void UpdateLine(ConnectionLine line)
    {
        if (!line.isVisible)
        {
            line.lineObject.gameObject.SetActive(false);
            return;
        }
        
        // 获取3D物体在屏幕上的位置
        Vector3 objectPosition = line.objectTransform.position + line.objectOffset;
        Vector3 screenPos = mainCamera.WorldToScreenPoint(objectPosition);
        
        // 检查3D物体是否在相机视野内
        bool isVisible = screenPos.z > 0 && 
                         screenPos.x > 0 && screenPos.x < Screen.width && 
                         screenPos.y > 0 && screenPos.y < Screen.height;
        
        // 如果设置了在视野外隐藏，且物体在视野外，则隐藏连线和UI
        if (line.hideWhenOutOfView && !isVisible)
        {
            line.lineObject.gameObject.SetActive(false);
            line.uiElement.gameObject.SetActive(false);
            return;
        }
        
        // 检查物体是否被遮挡
        if (line.hideWhenOccluded)
        {
            Vector3 directionToCamera = mainCamera.transform.position - objectPosition;
            float distanceToCamera = directionToCamera.magnitude;
            
            // 发射射线检测遮挡
            if (Physics.Raycast(objectPosition, directionToCamera, out RaycastHit hit, distanceToCamera, line.occlusionLayers))
            {
                // 如果射线击中的不是目标物体，说明被遮挡
                if (hit.transform != line.objectTransform)
                {
                    line.lineObject.gameObject.SetActive(false);
                    line.uiElement.gameObject.SetActive(false);
                    return;
                }
            }
        }
        
        // 显示连线和UI
        line.lineObject.gameObject.SetActive(true);
        line.uiElement.gameObject.SetActive(true);
        
        // 将屏幕坐标转换为Canvas坐标
        Vector2 objectCanvasPos;
        RectTransformUtility.ScreenPointToLocalPointInRectangle(
            targetCanvas.GetComponent<RectTransform>(),
            new Vector2(screenPos.x, screenPos.y),
            targetCanvas.worldCamera,
            out objectCanvasPos);
        
        // 获取UI元素在Canvas中的位置
        Vector2 uiPos = line.uiElement.anchoredPosition + line.uiOffset;
        
        // 设置连线位置、旋转和缩放
        UpdateLineTransform(line.lineObject, objectCanvasPos, uiPos);
    }
    
    /// <summary>
    /// 更新连线的变换（位置、旋转、缩放）
    /// </summary>
    private void UpdateLineTransform(RectTransform lineRect, Vector2 startPos, Vector2 endPos)
    {
        // 计算连线中点位置
        Vector2 midPoint = (startPos + endPos) / 2;
        lineRect.anchoredPosition = midPoint;
        
        // 计算连线长度
        float lineLength = Vector2.Distance(startPos, endPos);
        lineRect.sizeDelta = new Vector2(lineLength, lineRect.sizeDelta.y);
        
        // 计算连线旋转角度
        Vector2 direction = endPos - startPos;
        float angle = Mathf.Atan2(direction.y, direction.x) * Mathf.Rad2Deg;
        lineRect.localEulerAngles = new Vector3(0, 0, angle);
    }
    
    /// <summary>
    /// 添加新的连线
    /// </summary>
    public void AddConnectionLine(string name, Transform objectTransform, RectTransform uiElement, RectTransform lineObject)
    {
        ConnectionLine newLine = new ConnectionLine
        {
            name = name,
            objectTransform = objectTransform,
            uiElement = uiElement,
            lineObject = lineObject,
            lineImage = lineObject.GetComponent<Image>(),
            isVisible = true
        };
        
        connectionLines.Add(newLine);
        
        // 初始化新连线
        if (newLine.lineImage == null)
        {
            Debug.LogWarning($"连线 '{name}' 的lineObject上没有Image组件");
        }
        else
        {
            newLine.lineImage.color = newLine.lineColor;
            newLine.lineObject.sizeDelta = new Vector2(newLine.lineObject.sizeDelta.x, newLine.lineWidth);
        }
        
        // 立即更新连线
        UpdateLine(newLine);
    }
    
    /// <summary>
    /// 移除连线
    /// </summary>
    public void RemoveConnectionLine(string name)
    {
        ConnectionLine lineToRemove = connectionLines.Find(line => line.name == name);
        if (lineToRemove != null)
        {
            connectionLines.Remove(lineToRemove);
        }
    }
    
    /// <summary>
    /// 设置连线可见性
    /// </summary>
    public void SetLineVisibility(string name, bool isVisible)
    {
        ConnectionLine line = connectionLines.Find(l => l.name == name);
        if (line != null)
        {
            line.isVisible = isVisible;
            if (line.lineObject != null)
            {
                line.lineObject.gameObject.SetActive(isVisible);
            }
        }
    }
    
    /// <summary>
    /// 设置所有连线可见性
    /// </summary>
    public void SetAllLinesVisibility(bool isVisible)
    {
        foreach (ConnectionLine line in connectionLines)
        {
            line.isVisible = isVisible;
            if (line.lineObject != null)
            {
                line.lineObject.gameObject.SetActive(isVisible);
            }
        }
    }
    
    /// <summary>
    /// 更新连线颜色
    /// </summary>
    public void UpdateLineColor(string name, Color newColor)
    {
        ConnectionLine line = connectionLines.Find(l => l.name == name);
        if (line != null && line.lineImage != null)
        {
            line.lineColor = newColor;
            line.lineImage.color = newColor;
        }
    }
    
    /// <summary>
    /// 更新连线宽度
    /// </summary>
    public void UpdateLineWidth(string name, float newWidth)
    {
        ConnectionLine line = connectionLines.Find(l => l.name == name);
        if (line != null && line.lineObject != null)
        {
            line.lineWidth = newWidth;
            line.lineObject.sizeDelta = new Vector2(line.lineObject.sizeDelta.x, newWidth);
        }
    }
}