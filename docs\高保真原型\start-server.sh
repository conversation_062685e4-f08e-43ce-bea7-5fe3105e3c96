#!/bin/bash

echo "========================================"
echo "白云电气数字孪生系统 - 高保真原型"
echo "Unity WebGL集成演示服务器"
echo "========================================"
echo ""

echo "正在启动本地服务器..."
echo "服务器地址: http://localhost:8000"
echo ""
echo "可用页面:"
echo "- 模拟版本: http://localhost:8000/index.html"
echo "- Unity集成版: http://localhost:8000/unity-demo.html"
echo "- 基础演示: http://localhost:8000/demo.html"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "========================================"
echo ""

# 尝试使用Python启动服务器
if command -v python3 &> /dev/null; then
    echo "使用Python3启动服务器..."
    python3 -m http.server 8000
elif command -v python &> /dev/null; then
    echo "使用Python启动服务器..."
    python -m http.server 8000
elif command -v node &> /dev/null; then
    echo "使用Node.js启动服务器..."
    if command -v npx &> /dev/null; then
        npx serve . -p 8000
    else
        echo "错误: 未找到npx，请安装Node.js"
        exit 1
    fi
else
    echo ""
    echo "错误: 未找到Python或Node.js"
    echo "请安装Python或Node.js后重试"
    echo ""
    echo "Python安装: https://python.org"
    echo "Node.js安装: https://nodejs.org"
    echo ""
    exit 1
fi
