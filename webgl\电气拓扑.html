<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>桂林智源 SVG 数字化系统 - 电气系统拓扑图</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /**
         * 桂林智源 SVG 数字化系统 - 电气系统拓扑图页面样式
         * 深色科技主题，工业监控界面风格
         */

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #00d4ff;
            --secondary-color: #0099cc;
            --accent-color: #00ff88;
            --warning-color: #ffaa00;
            --error-color: #ff4444;
            --success-color: #00ff88;
            --bg-primary: #0a0f1c;
            --bg-secondary: #1a2332;
            --bg-tertiary: #2a3441;
            --text-primary: #ffffff;
            --text-secondary: #b8c5d6;
            --border-color: #3a4a5c;
            --shadow-color: rgba(0, 212, 255, 0.3);
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            width: 1366px;
            height: 768px;
            overflow: hidden;
            position: relative;
        }

        /* 背景动画效果 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 255, 136, 0.1) 0%, transparent 50%);
            animation: backgroundPulse 4s ease-in-out infinite alternate;
            pointer-events: none;
        }

        @keyframes backgroundPulse {
            0% {
                opacity: 0.3;
            }

            100% {
                opacity: 0.6;
            }
        }

        .container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 1;
        }

        /* 页面头部 */
        .page-header {
            background: linear-gradient(90deg, var(--bg-secondary), var(--bg-tertiary));
            border-bottom: 2px solid var(--primary-color);
            padding: 15px 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0, 212, 255, 0.3);
        }

        .page-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .page-title i {
            font-size: 28px;
            color: var(--accent-color);
        }

        .current-time {
            font-size: 16px;
            color: var(--text-secondary);
            font-family: 'Consolas', monospace;
        }

        /* 主要内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            gap: 20px;
        }

        /* 拓扑图容器 */
        .topology-container {
            flex: 1;
            background: var(--bg-secondary);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .topology-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            border-radius: 12px 12px 0 0;
        }

        .topology-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .topology-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .topology-status {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            background: var(--bg-tertiary);
            border-radius: 20px;
            font-size: 14px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
            animation: statusPulse 2s ease-in-out infinite;
        }

        @keyframes statusPulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }
        }

        /* 拓扑图显示区域 */
        .topology-display {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .topology-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 8px;
            transition: transform 0.3s ease;
        }

        .topology-image:hover {
            transform: scale(1.02);
        }

        /* 控制面板 */
        .control-panel {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 15px;
            padding: 15px;
            background: var(--bg-tertiary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .control-buttons {
            display: flex;
            gap: 10px;
        }

        .control-btn {
            padding: 8px 16px;
            background: var(--bg-secondary);
            border: 1px solid var(--primary-color);
            border-radius: 6px;
            color: var(--primary-color);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .control-btn:hover {
            background: var(--primary-color);
            color: var(--bg-primary);
            box-shadow: 0 2px 8px rgba(0, 212, 255, 0.4);
        }

        .zoom-info {
            color: var(--text-secondary);
            font-size: 14px;
            font-family: 'Consolas', monospace;
        }

        /* 响应式设计 */
        @media (max-width: 1366px) {
            body {
                width: 100vw;
                height: 100vh;
            }

            .page-header {
                padding: 12px 20px;
            }

            .main-content {
                padding: 15px;
            }
        }

        /* 加载动画 */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            gap: 15px;
            color: var(--text-secondary);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 页面头部 -->
        <header class="page-header" style="display: none;">
            <div class="page-title">
                <i class="fas fa-bolt"></i>
                <span>电气系统拓扑图</span>
            </div>
            <div class="current-time" id="currentTime">
                <!-- 当前时间将通过JavaScript动态更新 -->
            </div>
        </header>

        <!-- 主要内容区域 -->
        <div class="topology-display" id="topologyDisplay">
            <div class="loading" id="loadingIndicator">
                <div class="loading-spinner"></div>
                <span>正在加载拓扑图...</span>
            </div>
        </div>
    </div>
    </div>

    <script>
        /**
         * 桂林智源 SVG 数字化系统 - 电气系统拓扑图页面脚本
         * 处理拓扑图显示、交互和实时更新功能
         */

        // 全局变量
        let currentZoom = 1;
        let topologyImage = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function () {
            console.log('电气系统拓扑图页面初始化开始...');
            initTopologyPage();
            updateTime();
            setInterval(updateTime, 1000);
            console.log('电气系统拓扑图页面初始化完成');
        });

        /**
         * 初始化拓扑图页面
         */
        function initTopologyPage() {
            loadTopologyImage();
            initImageControls();
        }

        /**
         * 更新时间显示
         */
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        /**
         * 加载拓扑图图片
         */
        function loadTopologyImage() {
            const display = document.getElementById('topologyDisplay');
            const loading = document.getElementById('loadingIndicator');

            // 创建图片元素
            topologyImage = document.createElement('img');
            topologyImage.className = 'topology-image';
            topologyImage.src = './image/SVG系统拓扑图.png';
            topologyImage.alt = '电气系统拓扑图';

            // 图片加载完成后显示
            topologyImage.onload = function () {
                loading.style.display = 'none';
                display.appendChild(topologyImage);
                console.log('电气系统拓扑图加载完成');
            };

            // 图片加载失败处理
            topologyImage.onerror = function () {
                loading.innerHTML = `
                    <i class="fas fa-exclamation-triangle" style="font-size: 24px; color: var(--warning-color);"></i>
                    <span>拓扑图加载失败，请检查图片文件</span>
                `;
                console.error('电气系统拓扑图加载失败');
            };
        }

        /**
         * 初始化图片控制功能
         */
        function initImageControls() {
            // 鼠标滚轮缩放
            document.getElementById('topologyDisplay').addEventListener('wheel', function (e) {
                e.preventDefault();
                const delta = e.deltaY > 0 ? -0.1 : 0.1;
                zoomImage(currentZoom + delta);
            });
        }

        /**
         * 缩放图片
         * @param {number} zoom - 缩放比例
         */
        function zoomImage(zoom) {
            zoom = Math.max(0.5, Math.min(3, zoom)); // 限制缩放范围
            currentZoom = zoom;

            if (topologyImage) {
                topologyImage.style.transform = `scale(${zoom})`;
                document.getElementById('zoomInfo').textContent = `缩放比例: ${Math.round(zoom * 100)}%`;
            }
        }

        /**
         * 重置视图
         */
        function resetZoom() {
            zoomImage(1);
            console.log('重置拓扑图视图');
        }

        /**
         * 全屏显示
         */
        function fullScreen() {
            const element = document.getElementById('topologyDisplay');
            if (element.requestFullscreen) {
                element.requestFullscreen();
            } else if (element.webkitRequestFullscreen) {
                element.webkitRequestFullscreen();
            } else if (element.msRequestFullscreen) {
                element.msRequestFullscreen();
            }
            console.log('切换到全屏显示');
        }

        /**
         * 刷新拓扑图
         */
        function refreshTopology() {
            const display = document.getElementById('topologyDisplay');
            const loading = document.getElementById('loadingIndicator');

            // 显示加载状态
            if (topologyImage) {
                display.removeChild(topologyImage);
            }
            loading.style.display = 'flex';

            // 重新加载图片
            setTimeout(() => {
                loadTopologyImage();
            }, 500);

            console.log('刷新电气系统拓扑图');
        }
    </script>
</body>

</html>