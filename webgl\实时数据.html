<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>桂林智源 SVG 数字化系统 - 实时数据</title>
    <link rel="shortcut icon" href="TemplateData/favicon.ico">
    <link rel="stylesheet" href="styles.css">
    <!-- 引入 echarts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 实时数据页面专用样式 */
        .realtime-data-container {
            width: 1366px;
            height: 768px;
            margin: 0 auto;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            overflow: hidden;
            position: relative;
            font-family: var(--font-family);
        }

        /* 科技感背景动画 */
        .realtime-data-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(102, 224, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* 顶部标题栏 */
        .realtime-header {
            height: 80px;
            background: linear-gradient(90deg,
                rgba(26, 31, 46, 0.95) 0%,
                rgba(42, 49, 66, 0.95) 50%,
                rgba(26, 31, 46, 0.95) 100%);
            backdrop-filter: blur(10px);
            border-bottom: 2px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            box-shadow: var(--shadow-secondary);
            position: relative;
            z-index: 100;
        }

        .realtime-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg,
                transparent 0%,
                var(--primary-color) 25%,
                var(--accent-color) 50%,
                var(--primary-color) 75%,
                transparent 100%);
            background-size: 200% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        .realtime-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .realtime-title h1 {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }

        .realtime-title i {
            font-size: 32px;
            color: var(--primary-color);
            animation: logoSpin 4s linear infinite;
        }

        .realtime-time {
            font-family: var(--font-mono);
            font-size: 16px;
            font-weight: 600;
            color: var(--accent-color);
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.15) 0%,
                rgba(0, 153, 204, 0.15) 100%);
            padding: 10px 20px;
            border-radius: 20px;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 8px rgba(0, 212, 255, 0.2);
        }

        /* 主内容区域 */
        .realtime-main {
            height: calc(768px - 80px);
            display: grid;
            grid-template-columns: 300px 1fr 350px;
            gap: 20px;
            padding: 20px;
        }

        /* 左侧数据列表 */
        .data-list-panel {
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-primary);
            overflow: hidden;
        }

        .data-list-header {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.2) 0%,
                rgba(0, 153, 204, 0.2) 100%);
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .data-list-header h3 {
            margin: 0;
            font-size: 18px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .data-list-content {
            height: calc(100% - 60px);
            overflow-y: auto;
            padding: 10px;
        }

        .data-category {
            margin-bottom: 20px;
        }

        .category-title {
            font-size: 14px;
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 10px;
            padding: 8px 12px;
            background: rgba(0, 212, 255, 0.1);
            border-radius: 6px;
            border-left: 3px solid var(--primary-color);
        }

        .data-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            margin-bottom: 5px;
            background: rgba(0, 212, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.1);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .data-item:hover {
            background: rgba(0, 212, 255, 0.1);
            border-color: var(--primary-color);
            transform: translateX(5px);
        }

        .data-item.selected {
            background: rgba(0, 212, 255, 0.2);
            border-color: var(--primary-color);
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
        }

        .data-label {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .data-value {
            font-size: 13px;
            font-weight: bold;
            color: var(--text-primary);
            font-family: var(--font-mono);
        }

        /* 中间图表区域 */
        .chart-panel {
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-primary);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .chart-header {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.2) 0%,
                rgba(0, 153, 204, 0.2) 100%);
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chart-header h3 {
            margin: 0;
            font-size: 18px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chart-controls {
            display: flex;
            gap: 10px;
        }

        .chart-btn {
            background: transparent;
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .chart-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .chart-btn.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--bg-primary);
        }

        .chart-content {
            flex: 1;
            padding: 20px;
            position: relative;
        }

        .chart-container {
            width: 100%;
            height: 100%;
            background: rgba(0, 212, 255, 0.02);
            border: 1px solid rgba(0, 212, 255, 0.1);
            border-radius: 8px;
            position: relative;
        }

        /* 右侧控制面板 */
        .control-panel {
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-primary);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .control-header {
            background: linear-gradient(135deg,
                rgba(0, 212, 255, 0.2) 0%,
                rgba(0, 153, 204, 0.2) 100%);
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .control-header h3 {
            margin: 0;
            font-size: 18px;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .control-section {
            margin-bottom: 25px;
        }

        .control-section h4 {
            font-size: 14px;
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 12px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-label {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 6px;
            display: block;
        }

        .control-input {
            width: 100%;
            background: rgba(0, 212, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 6px;
            padding: 8px 12px;
            color: var(--text-primary);
            font-size: 13px;
            font-family: var(--font-mono);
        }

        .control-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 8px rgba(0, 212, 255, 0.3);
        }

        .control-select {
            width: 100%;
            background: rgba(0, 212, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 6px;
            padding: 8px 12px;
            color: var(--text-primary);
            font-size: 13px;
        }

        .control-button {
            width: 100%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 6px;
            padding: 10px 15px;
            color: white;
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 8px;
        }

        .control-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
        }

        .control-button:active {
            transform: translateY(0);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: rgba(0, 212, 255, 0.05);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 6px;
            margin-bottom: 8px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
            animation: pulse 2s infinite;
        }

        .status-dot.warning {
            background: var(--warning-color);
        }

        .status-dot.error {
            background: var(--error-color);
        }

        .status-text {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* 动画效果 */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes logoSpin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
    </style>
</head>
<body>
    <div class="realtime-data-container">
        <!-- 顶部标题栏 -->
        <header class="realtime-header" style="display: none;">
            <div class="realtime-title">
                <i class="fas fa-chart-line"></i>
                <h1>实时数据监控</h1>
            </div>
            <div class="realtime-time" id="currentTime"></div>
        </header>

        <!-- 主内容区域 -->
        <main class="realtime-main">
            <!-- 左侧数据列表 -->
            <aside class="data-list-panel">
                <div class="data-list-header">
                    <h3><i class="fas fa-list"></i>数据变量</h3>
                </div>
                <div class="data-list-content" id="dataListContent">
                    <!-- 数据项将通过JavaScript动态生成 -->
                </div>
            </aside>

            <!-- 中间图表区域 -->
            <section class="chart-panel">
                <div class="chart-header">
                    <h3><i class="fas fa-chart-area"></i>实时曲线图</h3>
                    <div class="chart-controls">
                        <button class="chart-btn active" data-type="line">线图</button>
                        <button class="chart-btn" data-type="bar">柱图</button>
                        <button class="chart-btn" data-type="scatter">散点</button>
                    </div>
                </div>
                <div class="chart-content">
                    <div class="chart-container" id="mainChart"></div>
                </div>
            </section>

            <!-- 右侧控制面板 -->
            <aside class="control-panel">
                <div class="control-header">
                    <h3><i class="fas fa-sliders-h"></i>控制面板</h3>
                </div>
                <div class="control-content">
                    <!-- 实时开关控制 -->
                    <div class="control-section">
                        <h4><i class="fas fa-power-off"></i>实时控制</h4>
                        <div class="control-group">
                            <label class="control-label">实时更新开关</label>
                            <select class="control-select" id="realtimeSwitch">
                                <option value="on">开启</option>
                                <option value="off">关闭</option>
                            </select>
                        </div>
                        <div class="control-group">
                            <label class="control-label">更新间隔 (秒)</label>
                            <input type="number" class="control-input" id="updateInterval" value="1" min="0.1" max="10" step="0.1">
                        </div>
                    </div>

                    <!-- 时间范围控制 -->
                    <div class="control-section">
                        <h4><i class="fas fa-clock"></i>时间范围</h4>
                        <div class="control-group">
                            <label class="control-label">开始时间</label>
                            <input type="datetime-local" class="control-input" id="startTime">
                        </div>
                        <div class="control-group">
                            <label class="control-label">结束时间</label>
                            <input type="datetime-local" class="control-input" id="endTime">
                        </div>
                        <button class="control-button" onclick="applyTimeRange()">
                            <i class="fas fa-check"></i> 应用时间范围
                        </button>
                    </div>

                    <!-- 数据查询 -->
                    <div class="control-section">
                        <h4><i class="fas fa-search"></i>数据查询</h4>
                        <div class="control-group">
                            <label class="control-label">查询条件</label>
                            <input type="text" class="control-input" id="queryCondition" placeholder="输入查询条件">
                        </div>
                        <button class="control-button" onclick="executeQuery()">
                            <i class="fas fa-play"></i> 执行查询
                        </button>
                        <button class="control-button" onclick="exportData()">
                            <i class="fas fa-download"></i> 导出数据
                        </button>
                    </div>

                    <!-- 系统状态 -->
                    <div class="control-section" style="display: none;">
                        <h4><i class="fas fa-info-circle"></i>系统状态</h4>
                        <div class="status-indicator">
                            <div class="status-dot" id="connectionStatus"></div>
                            <span class="status-text">数据连接状态</span>
                        </div>
                        <div class="status-indicator">
                            <div class="status-dot" id="updateStatus"></div>
                            <span class="status-text">数据更新状态</span>
                        </div>
                        <div class="status-indicator">
                            <div class="status-dot warning" id="systemStatus"></div>
                            <span class="status-text">系统运行状态</span>
                        </div>
                    </div>
                </div>
            </aside>
        </main>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('实时数据页面初始化开始...');
            initRealtimePage();
            updateTime();
            setInterval(updateTime, 1000);
            console.log('实时数据页面初始化完成');
        });

        /**
         * 初始化实时数据页面
         */
        function initRealtimePage() {
            initDataList();
            initChartPanel();
            initControlPanel();
            startDataSimulation();
        }

        /**
         * 更新时间显示
         */
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        // 全局变量
        let mainChart = null;
        let realtimeData = {};
        let selectedVariable = 'voltage_a';
        let isRealtime = true;
        let updateIntervalId = null;

        /**
         * 初始化数据列表
         */
        function initDataList() {
            const dataListContent = document.getElementById('dataListContent');

            // 定义数据变量分类
            const dataCategories = [
                {
                    title: '电压参数',
                    icon: 'fas fa-bolt',
                    items: [
                        { key: 'voltage_a', label: '电压A相', unit: 'kV', value: 0 },
                        { key: 'voltage_b', label: '电压B相', unit: 'kV', value: 0 },
                        { key: 'voltage_c', label: '电压C相', unit: 'kV', value: 0 },
                        { key: 'voltage_ab', label: '线电压AB', unit: 'kV', value: 0 },
                        { key: 'voltage_bc', label: '线电压BC', unit: 'kV', value: 0 },
                        { key: 'voltage_ca', label: '线电压CA', unit: 'kV', value: 0 }
                    ]
                },
                {
                    title: '电流参数',
                    icon: 'fas fa-flash',
                    items: [
                        { key: 'current_a', label: '电流A相', unit: 'A', value: 0 },
                        { key: 'current_b', label: '电流B相', unit: 'A', value: 0 },
                        { key: 'current_c', label: '电流C相', unit: 'A', value: 0 },
                        { key: 'current_load', label: '负载电流', unit: 'A', value: 0 },
                        { key: 'current_svg', label: 'SVG电流', unit: 'A', value: 0 }
                    ]
                },
                {
                    title: '功率参数',
                    icon: 'fas fa-tachometer-alt',
                    items: [
                        { key: 'power_active', label: '有功功率', unit: 'MW', value: 0 },
                        { key: 'power_reactive', label: '无功功率', unit: 'MVAr', value: 0 },
                        { key: 'power_factor', label: '功率因数', unit: '', value: 0 },
                        { key: 'frequency', label: '频率', unit: 'Hz', value: 50 }
                    ]
                },
                {
                    title: '温度参数',
                    icon: 'fas fa-thermometer-half',
                    items: [
                        { key: 'temp_inlet', label: '进水温度', unit: '°C', value: 25 },
                        { key: 'temp_outlet', label: '出水温度', unit: '°C', value: 35 },
                        { key: 'temp_ambient', label: '环境温度', unit: '°C', value: 22 },
                        { key: 'temp_cabinet', label: '柜体温度', unit: '°C', value: 28 }
                    ]
                }
            ];

            // 生成HTML内容
            let html = '';
            dataCategories.forEach(category => {
                html += `
                    <div class="data-category">
                        <div class="category-title">
                            <i class="${category.icon}"></i>
                            ${category.title}
                        </div>
                `;

                category.items.forEach(item => {
                    html += `
                        <div class="data-item ${item.key === selectedVariable ? 'selected' : ''}"
                             data-key="${item.key}" onclick="selectVariable('${item.key}')">
                            <span class="data-label">${item.label}</span>
                            <span class="data-value" id="value-${item.key}">${item.value.toFixed(2)} ${item.unit}</span>
                        </div>
                    `;

                    // 初始化实时数据
                    realtimeData[item.key] = {
                        value: item.value,
                        unit: item.unit,
                        history: [],
                        timestamps: []
                    };
                });

                html += '</div>';
            });

            dataListContent.innerHTML = html;
        }

        /**
         * 选择变量
         */
        function selectVariable(key) {
            // 更新选中状态
            document.querySelectorAll('.data-item').forEach(item => {
                item.classList.remove('selected');
            });
            document.querySelector(`[data-key="${key}"]`).classList.add('selected');

            selectedVariable = key;
            updateChart();
        }

        /**
         * 初始化图表面板
         */
        function initChartPanel() {
            // 初始化ECharts图表
            const chartContainer = document.getElementById('mainChart');
            mainChart = echarts.init(chartContainer);

            // 设置图表配置
            updateChart();

            // 绑定图表类型切换事件
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    updateChart();
                });
            });

            // 窗口大小变化时重新调整图表
            window.addEventListener('resize', function() {
                if (mainChart) {
                    mainChart.resize();
                }
            });
        }

        /**
         * 更新图表
         */
        function updateChart() {
            if (!mainChart || !realtimeData[selectedVariable]) return;

            const data = realtimeData[selectedVariable];
            const chartType = document.querySelector('.chart-btn.active').dataset.type;

            const option = {
                title: {
                    text: `${getVariableLabel(selectedVariable)} 实时监控`,
                    left: 'center',
                    textStyle: {
                        color: '#00d4ff',
                        fontSize: 16
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(26, 31, 46, 0.9)',
                    borderColor: '#00d4ff',
                    textStyle: {
                        color: '#ffffff'
                    }
                },
                grid: {
                    left: '10%',
                    right: '10%',
                    bottom: '15%',
                    top: '20%'
                },
                xAxis: {
                    type: 'category',
                    data: data.timestamps,
                    axisLine: {
                        lineStyle: { color: '#00d4ff' }
                    },
                    axisLabel: {
                        color: '#b8c5d6',
                        formatter: function(value) {
                            return new Date(value).toLocaleTimeString();
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: { color: '#00d4ff' }
                    },
                    axisLabel: {
                        color: '#b8c5d6',
                        formatter: `{value} ${data.unit}`
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(0, 212, 255, 0.1)'
                        }
                    }
                },
                series: [{
                    name: getVariableLabel(selectedVariable),
                    type: chartType,
                    data: data.history,
                    itemStyle: {
                        color: '#00d4ff'
                    },
                    lineStyle: {
                        color: '#00d4ff',
                        width: 2
                    },
                    areaStyle: chartType === 'line' ? {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                { offset: 0, color: 'rgba(0, 212, 255, 0.3)' },
                                { offset: 1, color: 'rgba(0, 212, 255, 0.05)' }
                            ]
                        }
                    } : null,
                    smooth: true,
                    animation: true
                }]
            };

            mainChart.setOption(option);
        }

        /**
         * 获取变量标签
         */
        function getVariableLabel(key) {
            const labels = {
                'voltage_a': '电压A相', 'voltage_b': '电压B相', 'voltage_c': '电压C相',
                'voltage_ab': '线电压AB', 'voltage_bc': '线电压BC', 'voltage_ca': '线电压CA',
                'current_a': '电流A相', 'current_b': '电流B相', 'current_c': '电流C相',
                'current_load': '负载电流', 'current_svg': 'SVG电流',
                'power_active': '有功功率', 'power_reactive': '无功功率',
                'power_factor': '功率因数', 'frequency': '频率',
                'temp_inlet': '进水温度', 'temp_outlet': '出水温度',
                'temp_ambient': '环境温度', 'temp_cabinet': '柜体温度'
            };
            return labels[key] || key;
        }

        /**
         * 初始化控制面板
         */
        function initControlPanel() {
            // 设置默认时间范围
            const now = new Date();
            const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

            document.getElementById('startTime').value = formatDateTimeLocal(oneHourAgo);
            document.getElementById('endTime').value = formatDateTimeLocal(now);

            // 绑定实时开关事件
            document.getElementById('realtimeSwitch').addEventListener('change', function() {
                isRealtime = this.value === 'on';
                if (isRealtime) {
                    startDataSimulation();
                } else {
                    stopDataSimulation();
                }
                updateStatusIndicators();
            });

            // 绑定更新间隔事件
            document.getElementById('updateInterval').addEventListener('change', function() {
                if (isRealtime) {
                    stopDataSimulation();
                    startDataSimulation();
                }
            });

            // 初始化状态指示器
            updateStatusIndicators();
        }

        /**
         * 格式化日期时间为本地输入格式
         */
        function formatDateTimeLocal(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            return `${year}-${month}-${day}T${hours}:${minutes}`;
        }

        /**
         * 开始数据模拟
         */
        function startDataSimulation() {
            if (updateIntervalId) {
                clearInterval(updateIntervalId);
            }

            const interval = parseFloat(document.getElementById('updateInterval').value) * 1000;

            updateIntervalId = setInterval(() => {
                updateRealtimeData();
                updateDataDisplay();
                updateChart();
            }, interval);

            console.log(`数据模拟已启动，更新间隔: ${interval}ms`);
        }

        /**
         * 停止数据模拟
         */
        function stopDataSimulation() {
            if (updateIntervalId) {
                clearInterval(updateIntervalId);
                updateIntervalId = null;
            }
            console.log('数据模拟已停止');
        }

        /**
         * 更新实时数据
         */
        function updateRealtimeData() {
            const now = new Date();

            Object.keys(realtimeData).forEach(key => {
                const data = realtimeData[key];
                let newValue;

                // 根据不同类型生成模拟数据
                switch(key) {
                    case 'voltage_a':
                    case 'voltage_b':
                    case 'voltage_c':
                        newValue = 10.0 + Math.sin(now.getTime() / 10000) * 0.5 + (Math.random() - 0.5) * 0.2;
                        break;
                    case 'voltage_ab':
                    case 'voltage_bc':
                    case 'voltage_ca':
                        newValue = 17.3 + Math.sin(now.getTime() / 8000) * 0.8 + (Math.random() - 0.5) * 0.3;
                        break;
                    case 'current_a':
                    case 'current_b':
                    case 'current_c':
                        newValue = 150 + Math.sin(now.getTime() / 12000) * 20 + (Math.random() - 0.5) * 10;
                        break;
                    case 'current_load':
                        newValue = 450 + Math.sin(now.getTime() / 15000) * 50 + (Math.random() - 0.5) * 20;
                        break;
                    case 'current_svg':
                        newValue = 200 + Math.sin(now.getTime() / 9000) * 30 + (Math.random() - 0.5) * 15;
                        break;
                    case 'power_active':
                        newValue = 2.5 + Math.sin(now.getTime() / 20000) * 0.3 + (Math.random() - 0.5) * 0.1;
                        break;
                    case 'power_reactive':
                        newValue = 1.8 + Math.sin(now.getTime() / 18000) * 0.4 + (Math.random() - 0.5) * 0.2;
                        break;
                    case 'power_factor':
                        newValue = 0.95 + Math.sin(now.getTime() / 25000) * 0.03 + (Math.random() - 0.5) * 0.01;
                        break;
                    case 'frequency':
                        newValue = 50.0 + Math.sin(now.getTime() / 30000) * 0.1 + (Math.random() - 0.5) * 0.05;
                        break;
                    case 'temp_inlet':
                        newValue = 25 + Math.sin(now.getTime() / 40000) * 2 + (Math.random() - 0.5) * 1;
                        break;
                    case 'temp_outlet':
                        newValue = 35 + Math.sin(now.getTime() / 35000) * 3 + (Math.random() - 0.5) * 1.5;
                        break;
                    case 'temp_ambient':
                        newValue = 22 + Math.sin(now.getTime() / 60000) * 1 + (Math.random() - 0.5) * 0.5;
                        break;
                    case 'temp_cabinet':
                        newValue = 28 + Math.sin(now.getTime() / 45000) * 2 + (Math.random() - 0.5) * 1;
                        break;
                    default:
                        newValue = data.value + (Math.random() - 0.5) * 0.1;
                }

                data.value = Math.max(0, newValue);
                data.history.push(data.value);
                data.timestamps.push(now.getTime());

                // 保持历史数据在合理范围内（最多100个点）
                if (data.history.length > 100) {
                    data.history.shift();
                    data.timestamps.shift();
                }
            });
        }

        /**
         * 更新数据显示
         */
        function updateDataDisplay() {
            Object.keys(realtimeData).forEach(key => {
                const element = document.getElementById(`value-${key}`);
                if (element) {
                    const data = realtimeData[key];
                    const precision = key.includes('factor') ? 3 : 2;
                    element.textContent = `${data.value.toFixed(precision)} ${data.unit}`;
                }
            });
        }

        /**
         * 更新状态指示器
         */
        function updateStatusIndicators() {
            const connectionStatus = document.getElementById('connectionStatus');
            const updateStatus = document.getElementById('updateStatus');
            const systemStatus = document.getElementById('systemStatus');

            // 连接状态（模拟）
            connectionStatus.className = 'status-dot';

            // 更新状态
            if (isRealtime) {
                updateStatus.className = 'status-dot';
            } else {
                updateStatus.className = 'status-dot warning';
            }

            // 系统状态（随机模拟）
            const statuses = ['status-dot', 'status-dot warning', 'status-dot error'];
            systemStatus.className = statuses[Math.floor(Math.random() * statuses.length)];
        }

        /**
         * 应用时间范围
         */
        function applyTimeRange() {
            const startTime = document.getElementById('startTime').value;
            const endTime = document.getElementById('endTime').value;

            if (startTime && endTime) {
                console.log(`应用时间范围: ${startTime} 到 ${endTime}`);
                // 这里可以添加历史数据查询逻辑
                alert(`时间范围已设置：${startTime} 到 ${endTime}`);
            } else {
                alert('请选择完整的时间范围');
            }
        }

        /**
         * 执行查询
         */
        function executeQuery() {
            const condition = document.getElementById('queryCondition').value;
            if (condition.trim()) {
                console.log(`执行查询: ${condition}`);
                alert(`查询条件：${condition}\n（这是演示功能）`);
            } else {
                alert('请输入查询条件');
            }
        }

        /**
         * 导出数据
         */
        function exportData() {
            const data = realtimeData[selectedVariable];
            const csvContent = "data:text/csv;charset=utf-8,"
                + "时间,数值\n"
                + data.timestamps.map((time, index) =>
                    `${new Date(time).toLocaleString()},${data.history[index]}`
                ).join("\n");

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `${getVariableLabel(selectedVariable)}_数据.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            console.log('数据导出完成');
        }
    </script>
</body>
</html>
