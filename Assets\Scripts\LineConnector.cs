using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// 连线连接器 - 用于设备控制器中集成连线管理功能
/// </summary>
public class LineConnector : MonoBehaviour
{
    [Header("连线管理")]
    [Tooltip("连线管理器引用")]
    public LineManager lineManager;
    
    [System.Serializable]
    public class DeviceUIConnection
    {
        [Tooltip("连接名称")]
        public string connectionName;
        
        [Tooltip("设备上的3D开关或组件")]
        public Transform deviceComponent;
        
        [Tooltip("UI上的说明文本或图标")]
        public RectTransform uiElement;
        
        [Tooltip("连线预制体")]
        public GameObject linePrefab;
        
        [Tooltip("连线颜色")]
        public Color lineColor = Color.white;
        
        [Tooltip("连线宽度")]
        public float lineWidth = 2f;
        
        [Tooltip("3D物体连接点偏移")]
        public Vector3 objectOffset = Vector3.zero;
        
        [Tooltip("UI元素连接点偏移")]
        public Vector2 uiOffset = Vector2.zero;
        
        [HideInInspector]
        public RectTransform lineInstance;
    }
    
    [Tooltip("设备与UI的连接配置列表")]
    public List<DeviceUIConnection> connections = new List<DeviceUIConnection>();
    
    [Tooltip("Canvas引用")]
    public Canvas targetCanvas;
    
    [Tooltip("连线的渲染顺序，负值使连线显示在其他UI元素后面")]
    public int lineSortingOrder = -1;
    
    void Start()
    {
        // 确保有连线管理器
        if (lineManager == null)
        {
            lineManager = FindObjectOfType<LineManager>();
            if (lineManager == null)
            {
                // 如果场景中没有LineManager，创建一个
                GameObject lineManagerObj = new GameObject("LineManager");
                lineManager = lineManagerObj.AddComponent<LineManager>();
                lineManager.targetCanvas = targetCanvas;
                lineManager.mainCamera = Camera.main;
            }
        }
        
        // 初始化所有连接
        InitializeConnections();
    }
    
    /// <summary>
    /// 初始化所有连接
    /// </summary>
    private void InitializeConnections()
    {
        foreach (DeviceUIConnection connection in connections)
        {
            if (connection.deviceComponent == null || connection.uiElement == null)
            {
                Debug.LogWarning($"连接 '{connection.connectionName}' 缺少必要组件引用");
                continue;
            }
            
            // 创建连线实例
            if (connection.linePrefab != null)
            {
                GameObject lineObj = Instantiate(connection.linePrefab, targetCanvas.transform);
                connection.lineInstance = lineObj.GetComponent<RectTransform>();
                
                // 添加到连线管理器
                if (connection.lineInstance != null)
                {
                    // 获取或添加Image组件
                    Image lineImage = connection.lineInstance.GetComponent<Image>();
                    if (lineImage == null)
                    {
                        lineImage = connection.lineInstance.gameObject.AddComponent<Image>();
                    }
                    
                    // 设置连线属性
                    lineImage.color = connection.lineColor;
                    
                    // 设置渲染顺序，确保连线显示在其他UI元素后面
                    Canvas.ForceUpdateCanvases();
                    // 通过Image组件设置排序顺序
                    if (lineImage != null)
                    {
                        lineImage.canvas.sortingOrder = lineSortingOrder; // 使用可配置的排序顺序
                    }
                    
                    // 创建连线管理器中的连线
                    LineManager.ConnectionLine newLine = new LineManager.ConnectionLine
                    {
                        name = connection.connectionName,
                        objectTransform = connection.deviceComponent,
                        uiElement = connection.uiElement,
                        lineObject = connection.lineInstance,
                        lineImage = lineImage,
                        lineColor = connection.lineColor,
                        lineWidth = connection.lineWidth,
                        objectOffset = connection.objectOffset,
                        uiOffset = connection.uiOffset,
                        isVisible = true
                    };
                    
                    // 添加到连线管理器
                    lineManager.connectionLines.Add(newLine);
                }
            }
            else
            {
                Debug.LogWarning($"连接 '{connection.connectionName}' 没有设置连线预制体");
            }
        }
    }
    
    /// <summary>
    /// 设置连接可见性
    /// </summary>
    public void SetConnectionVisibility(string connectionName, bool isVisible)
    {
        // 在连线管理器中设置可见性
        if (lineManager != null)
        {
            lineManager.SetLineVisibility(connectionName, isVisible);
        }
        
        // 同时更新本地连接状态
        DeviceUIConnection connection = connections.Find(c => c.connectionName == connectionName);
        if (connection != null && connection.lineInstance != null)
        {
            connection.lineInstance.gameObject.SetActive(isVisible);
        }
    }
    
    /// <summary>
    /// 设置所有连接可见性
    /// </summary>
    public void SetAllConnectionsVisibility(bool isVisible)
    {
        // 在连线管理器中设置所有可见性
        if (lineManager != null)
        {
            lineManager.SetAllLinesVisibility(isVisible);
        }
        
        // 同时更新所有本地连接状态
        foreach (DeviceUIConnection connection in connections)
        {
            if (connection.lineInstance != null)
            {
                connection.lineInstance.gameObject.SetActive(isVisible);
            }
        }
    }
    
    /// <summary>
    /// 更新连接颜色
    /// </summary>
    public void UpdateConnectionColor(string connectionName, Color newColor)
    {
        // 在连线管理器中更新颜色
        if (lineManager != null)
        {
            lineManager.UpdateLineColor(connectionName, newColor);
        }
        
        // 同时更新本地连接颜色
        DeviceUIConnection connection = connections.Find(c => c.connectionName == connectionName);
        if (connection != null)
        {
            connection.lineColor = newColor;
        }
    }
    
    /// <summary>
    /// 更新连接宽度
    /// </summary>
    public void UpdateConnectionWidth(string connectionName, float newWidth)
    {
        // 在连线管理器中更新宽度
        if (lineManager != null)
        {
            lineManager.UpdateLineWidth(connectionName, newWidth);
        }
        
        // 同时更新本地连接宽度
        DeviceUIConnection connection = connections.Find(c => c.connectionName == connectionName);
        if (connection != null)
        {
            connection.lineWidth = newWidth;
        }
    }
    
    /// <summary>
    /// 添加新连接
    /// </summary>
    public void AddConnection(string name, Transform deviceComponent, RectTransform uiElement, GameObject linePrefab, Color lineColor, float lineWidth)
    {
        if (deviceComponent == null || uiElement == null || linePrefab == null)
        {
            Debug.LogError("添加连接失败：缺少必要组件引用");
            return;
        }
        
        // 创建新连接
        DeviceUIConnection newConnection = new DeviceUIConnection
        {
            connectionName = name,
            deviceComponent = deviceComponent,
            uiElement = uiElement,
            linePrefab = linePrefab,
            lineColor = lineColor,
            lineWidth = lineWidth
        };
        
        // 添加到连接列表
        connections.Add(newConnection);
        
        // 创建连线实例
        GameObject lineObj = Instantiate(linePrefab, targetCanvas.transform);
        newConnection.lineInstance = lineObj.GetComponent<RectTransform>();
        
        // 添加到连线管理器
        if (newConnection.lineInstance != null && lineManager != null)
        {
            // 设置渲染顺序，确保连线显示在其他UI元素后面
            Canvas.ForceUpdateCanvases();
            
            // 获取或添加Image组件
            Image lineImage = newConnection.lineInstance.GetComponent<Image>();
            if (lineImage == null)
            {
                lineImage = newConnection.lineInstance.gameObject.AddComponent<Image>();
            }
            
            // 通过Image组件设置排序顺序
            if (lineImage != null)
            {
                lineImage.canvas.sortingOrder = lineSortingOrder; // 使用可配置的排序顺序
            }
            
            // 设置连线属性
            lineImage.color = lineColor;
            
            // 添加到连线管理器
            lineManager.AddConnectionLine(
                name,
                deviceComponent,
                uiElement,
                newConnection.lineInstance
            );
            
            // 更新连线宽度
            lineManager.UpdateLineWidth(name, lineWidth);
        }
    }
    
    /// <summary>
    /// 移除连接
    /// </summary>
    public void RemoveConnection(string connectionName)
    {
        // 在连线管理器中移除连线
        if (lineManager != null)
        {
            lineManager.RemoveConnectionLine(connectionName);
        }
        
        // 查找并移除本地连接
        DeviceUIConnection connection = connections.Find(c => c.connectionName == connectionName);
        if (connection != null)
        {
            // 销毁连线实例
            if (connection.lineInstance != null)
            {
                Destroy(connection.lineInstance.gameObject);
            }
            
            // 从列表中移除
            connections.Remove(connection);
        }
    }
}