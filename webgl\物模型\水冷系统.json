{"properties": [{"id": "HMI_33525_0", "name": "水冷系统自动模式", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 1, "datatype": {"max": 100, "min": 0, "step": 1, "type": "integer", "unit": ""}}, {"id": "HMI_33525_2", "name": "水冷系统远程控制", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 1, "datatype": {"max": 100, "min": 0, "step": 1, "type": "integer", "unit": ""}}, {"id": "HMI_33525_10", "name": "水冷运行状态", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 1, "datatype": {"max": 100, "min": 0, "step": 1, "type": "integer", "unit": ""}}, {"id": "HMI_33500", "name": "供水温度（℃）", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 1, "datatype": {"max": 100, "min": 0, "step": 1, "type": "integer", "unit": ""}}, {"id": "HMI_33501", "name": "回水温度（℃）", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 1, "datatype": {"max": 100, "min": 0, "step": 1, "type": "integer", "unit": ""}}, {"id": "HMI_33502", "name": "供水压力（bar）", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 1, "datatype": {"max": 100, "min": 0, "step": 1, "type": "integer", "unit": ""}}, {"id": "HMI_33503", "name": "回水压力（bar）", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 1, "datatype": {"max": 100, "min": 0, "step": 1, "type": "integer", "unit": ""}}, {"id": "HMI_33504", "name": "冷却水流量（L/min）", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 1, "datatype": {"max": 100, "min": 0, "step": 1, "type": "integer", "unit": ""}}, {"id": "HMI_33505", "name": "冷却水电导率（μs/cm）", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 1, "datatype": {"max": 100, "min": 0, "step": 1, "type": "integer", "unit": ""}}, {"id": "HMI_33506", "name": "阀室温度（℃）", "isChart": 1, "isMonitor": 1, "isHistory": 1, "isSharePerm": 1, "isReadonly": 1, "datatype": {"max": 100, "min": 0, "step": 1, "type": "integer", "unit": ""}}], "functions": [], "events": []}