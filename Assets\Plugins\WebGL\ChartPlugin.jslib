mergeInto(LibraryManager.library, {
    InitChartsInternal: function() {
        // 调用WebGL页面中的initCharts函数
        console.log('[ChartPlugin] 开始初始化图表...');
        if (typeof window !== 'undefined') {
            if (window.initCharts) {
                console.log('[ChartPlugin] window.initCharts 函数已找到，开始调用...');
                window.initCharts();
                console.log('[ChartPlugin] 图表初始化完成');
            } else {
                console.error('[ChartPlugin] window.initCharts 函数未找到！');
            }
        } else {
            console.error('[ChartPlugin] window 对象未定义！');
        }
    }
});