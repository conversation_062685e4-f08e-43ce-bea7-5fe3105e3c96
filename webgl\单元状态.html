<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>桂林智源 SVG 数字化系统 - 单元状态监控</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /**
         * 桂林智源 SVG 数字化系统 - 单元状态监控页面样式
         * 深色科技主题，工业监控界面风格
         */

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #00d4ff;
            --secondary-color: #0099cc;
            --accent-color: #00ff88;
            --warning-color: #ffaa00;
            --error-color: #ff4444;
            --success-color: #00ff88;
            --bg-primary: #0a0f1c;
            --bg-secondary: #1a2332;
            --bg-tertiary: #2a3441;
            --text-primary: #ffffff;
            --text-secondary: #b8c5d6;
            --border-color: #3a4a5c;
            --shadow-color: rgba(0, 212, 255, 0.3);
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            width: 1366px;
            height: 768px;
            overflow: hidden;
            position: relative;
        }

        /* 背景动画效果 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 255, 136, 0.1) 0%, transparent 50%);
            animation: backgroundPulse 4s ease-in-out infinite alternate;
            pointer-events: none;
        }

        @keyframes backgroundPulse {
            0% { opacity: 0.3; }
            100% { opacity: 0.6; }
        }

        .container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 1;
        }

        /* 页面头部 */
        .page-header {
            background: linear-gradient(90deg, var(--bg-secondary), var(--bg-tertiary));
            border-bottom: 2px solid var(--primary-color);
            padding: 15px 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0, 212, 255, 0.3);
        }

        .page-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .page-title i {
            font-size: 28px;
            color: var(--accent-color);
        }

        .current-time {
            font-size: 16px;
            color: var(--text-secondary);
            font-family: 'Consolas', monospace;
        }

        /* 主要内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            gap: 20px;
            padding: 20px;
        }

        /* 左侧单元列表 */
        .unit-list-panel {
            width: 300px;
            background: var(--bg-secondary);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .unit-list-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            border-radius: 12px 12px 0 0;
        }

        .panel-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
            font-size: 16px;
            font-weight: 600;
        }

        .unit-list {
            flex: 1;
            overflow-y: auto;
        }

        .unit-item {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .unit-item:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(0, 212, 255, 0.3);
        }

        .unit-item.selected {
            background: var(--primary-color);
            color: var(--bg-primary);
            border-color: var(--primary-color);
        }

        .unit-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .unit-name {
            font-size: 14px;
            font-weight: 600;
        }

        .unit-phase {
            font-size: 12px;
            opacity: 0.8;
        }

        .unit-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .unit-status.normal { background: var(--success-color); }
        .unit-status.warning { background: var(--warning-color); }
        .unit-status.error { background: var(--error-color); }

        /* 右侧详情面板 */
        .unit-detail-panel {
            flex: 1;
            background: var(--bg-secondary);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .unit-detail-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-color), var(--primary-color));
            border-radius: 12px 12px 0 0;
        }

        .detail-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .detail-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 18px;
            font-weight: 600;
        }

        .detail-status {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            background: var(--bg-tertiary);
            border-radius: 20px;
            font-size: 14px;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: statusPulse 2s ease-in-out infinite;
        }

        @keyframes statusPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* 参数网格 */
        .parameters-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            flex: 1;
        }

        .parameter-card {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .parameter-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(0, 212, 255, 0.3);
        }

        .parameter-label {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .parameter-value {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
            font-family: 'Consolas', monospace;
        }

        .parameter-unit {
            font-size: 11px;
            color: var(--text-secondary);
        }

        /* 响应式设计 */
        @media (max-width: 1366px) {
            body {
                width: 100vw;
                height: 100vh;
            }
            
            .page-header {
                padding: 12px 20px;
            }
            
            .main-content {
                padding: 15px;
                gap: 15px;
            }
            
            .unit-list-panel {
                width: 250px;
            }
            
            .parameters-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* 空状态 */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            flex: 1;
            color: var(--text-secondary);
            gap: 15px;
        }

        .empty-state i {
            font-size: 48px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <header class="page-header" style="display: none;">
            <div class="page-title">
                <i class="fas fa-microchip"></i>
                <span>单元状态监控</span>
            </div>
            <div class="current-time" id="currentTime">
                <!-- 当前时间将通过JavaScript动态更新 -->
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 左侧单元列表 -->
            <aside class="unit-list-panel">
                <div class="panel-header">
                    <i class="fas fa-list"></i>
                    <span>单元列表</span>
                </div>
                <div class="unit-list" id="unitList">
                    <!-- 单元列表将通过JavaScript动态生成 -->
                </div>
            </aside>

            <!-- 右侧详情面板 -->
            <section class="unit-detail-panel">
                <div class="detail-header">
                    <div class="detail-title">
                        <i class="fas fa-info-circle"></i>
                        <span id="detailTitle">请选择单元</span>
                    </div>
                    <div class="detail-status" id="detailStatus" style="display: none;">
                        <div class="status-indicator" id="statusIndicator"></div>
                        <span id="statusText">正常运行</span>
                    </div>
                </div>

                <div id="detailContent">
                    <div class="empty-state">
                        <i class="fas fa-mouse-pointer"></i>
                        <p>请从左侧列表中选择一个单元查看详细信息</p>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script>
        /**
         * 桂林智源 SVG 数字化系统 - 单元状态监控页面脚本
         * 处理单元状态数据展示、选择和实时更新功能
         */

        // 全局变量
        let unitsData = [];
        let selectedUnitId = null;
        let updateInterval = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('单元状态监控页面初始化开始...');
            initUnitStatusPage();
            updateTime();
            setInterval(updateTime, 1000);
            console.log('单元状态监控页面初始化完成');
        });

        /**
         * 初始化单元状态页面
         */
        function initUnitStatusPage() {
            generateUnitsData();
            renderUnitList();
            startRealTimeUpdate();
        }

        /**
         * 更新时间显示
         */
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        /**
         * 生成单元数据
         */
        function generateUnitsData() {
            unitsData = [];
            const phases = ['A', 'B', 'C'];
            
            phases.forEach(phase => {
                for (let i = 1; i <= 12; i++) {
                    const unitId = `${phase}${i.toString().padStart(2, '0')}`;
                    const status = Math.random() > 0.8 ? (Math.random() > 0.5 ? 'warning' : 'error') : 'normal';
                    
                    unitsData.push({
                        id: unitId,
                        name: `单元${unitId}`,
                        phase: `${phase}相`,
                        status: status,
                        parameters: generateUnitParameters()
                    });
                }
            });
        }

        /**
         * 生成单元参数
         */
        function generateUnitParameters() {
            return {
                voltage: (220 + (Math.random() - 0.5) * 20).toFixed(1),
                current: (10 + Math.random() * 5).toFixed(2),
                power: (2000 + Math.random() * 500).toFixed(0),
                frequency: (50 + (Math.random() - 0.5) * 0.5).toFixed(2),
                temperature: (35 + Math.random() * 10).toFixed(1),
                efficiency: (95 + Math.random() * 4).toFixed(1),
                thd: (Math.random() * 5).toFixed(2),
                powerFactor: (0.9 + Math.random() * 0.09).toFixed(3),
                runTime: Math.floor(Math.random() * 8760)
            };
        }

        /**
         * 渲染单元列表
         */
        function renderUnitList() {
            const unitList = document.getElementById('unitList');
            
            const html = unitsData.map(unit => `
                <div class="unit-item ${unit.id === selectedUnitId ? 'selected' : ''}" 
                     data-unit-id="${unit.id}" onclick="selectUnit('${unit.id}')">
                    <div class="unit-info">
                        <div class="unit-name">${unit.name}</div>
                        <div class="unit-phase">${unit.phase}</div>
                    </div>
                    <div class="unit-status ${unit.status}"></div>
                </div>
            `).join('');
            
            unitList.innerHTML = html;
        }

        /**
         * 选择单元
         */
        function selectUnit(unitId) {
            selectedUnitId = unitId;
            renderUnitList();
            showUnitDetail(unitId);
        }

        /**
         * 显示单元详情
         */
        function showUnitDetail(unitId) {
            const unit = unitsData.find(u => u.id === unitId);
            if (!unit) return;
            
            // 更新标题和状态
            document.getElementById('detailTitle').textContent = `${unit.name} - ${unit.phase}`;
            document.getElementById('detailStatus').style.display = 'flex';
            
            const statusIndicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            
            statusIndicator.className = `status-indicator ${unit.status}`;
            statusIndicator.style.background = getStatusColor(unit.status);
            
            switch(unit.status) {
                case 'normal':
                    statusText.textContent = '正常运行';
                    break;
                case 'warning':
                    statusText.textContent = '告警状态';
                    break;
                case 'error':
                    statusText.textContent = '故障状态';
                    break;
            }
            
            // 渲染参数网格
            const detailContent = document.getElementById('detailContent');
            detailContent.innerHTML = `
                <div class="parameters-grid">
                    <div class="parameter-card">
                        <div class="parameter-label">电压</div>
                        <div class="parameter-value">${unit.parameters.voltage}</div>
                        <div class="parameter-unit">V</div>
                    </div>
                    <div class="parameter-card">
                        <div class="parameter-label">电流</div>
                        <div class="parameter-value">${unit.parameters.current}</div>
                        <div class="parameter-unit">A</div>
                    </div>
                    <div class="parameter-card">
                        <div class="parameter-label">功率</div>
                        <div class="parameter-value">${unit.parameters.power}</div>
                        <div class="parameter-unit">W</div>
                    </div>
                    <div class="parameter-card">
                        <div class="parameter-label">频率</div>
                        <div class="parameter-value">${unit.parameters.frequency}</div>
                        <div class="parameter-unit">Hz</div>
                    </div>
                    <div class="parameter-card">
                        <div class="parameter-label">温度</div>
                        <div class="parameter-value">${unit.parameters.temperature}</div>
                        <div class="parameter-unit">°C</div>
                    </div>
                    <div class="parameter-card">
                        <div class="parameter-label">效率</div>
                        <div class="parameter-value">${unit.parameters.efficiency}</div>
                        <div class="parameter-unit">%</div>
                    </div>
                    <div class="parameter-card">
                        <div class="parameter-label">THD</div>
                        <div class="parameter-value">${unit.parameters.thd}</div>
                        <div class="parameter-unit">%</div>
                    </div>
                    <div class="parameter-card">
                        <div class="parameter-label">功率因数</div>
                        <div class="parameter-value">${unit.parameters.powerFactor}</div>
                        <div class="parameter-unit"></div>
                    </div>
                    <div class="parameter-card">
                        <div class="parameter-label">运行时间</div>
                        <div class="parameter-value">${unit.parameters.runTime}</div>
                        <div class="parameter-unit">小时</div>
                    </div>
                </div>
            `;
        }

        /**
         * 获取状态颜色
         */
        function getStatusColor(status) {
            switch(status) {
                case 'normal': return 'var(--success-color)';
                case 'warning': return 'var(--warning-color)';
                case 'error': return 'var(--error-color)';
                default: return 'var(--border-color)';
            }
        }

        /**
         * 开始实时更新
         */
        function startRealTimeUpdate() {
            updateInterval = setInterval(() => {
                updateUnitsData();
                renderUnitList();
                if (selectedUnitId) {
                    showUnitDetail(selectedUnitId);
                }
            }, 5000);
        }

        /**
         * 更新单元数据
         */
        function updateUnitsData() {
            unitsData.forEach(unit => {
                // 随机更新状态
                if (Math.random() > 0.95) {
                    const statuses = ['normal', 'warning', 'error'];
                    const weights = [0.8, 0.15, 0.05];
                    
                    let random = Math.random();
                    let cumulativeWeight = 0;
                    
                    for (let i = 0; i < statuses.length; i++) {
                        cumulativeWeight += weights[i];
                        if (random <= cumulativeWeight) {
                            unit.status = statuses[i];
                            break;
                        }
                    }
                }
                
                // 更新参数
                const params = unit.parameters;
                params.voltage = (220 + (Math.random() - 0.5) * 20).toFixed(1);
                params.current = (10 + Math.random() * 5).toFixed(2);
                params.power = (2000 + Math.random() * 500).toFixed(0);
                params.frequency = (50 + (Math.random() - 0.5) * 0.5).toFixed(2);
                params.temperature = (35 + Math.random() * 10).toFixed(1);
                params.efficiency = (95 + Math.random() * 4).toFixed(1);
                params.thd = (Math.random() * 5).toFixed(2);
                params.powerFactor = (0.9 + Math.random() * 0.09).toFixed(3);
                params.runTime += Math.floor(Math.random() * 2);
            });
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        });
    </script>
</body>
</html>
