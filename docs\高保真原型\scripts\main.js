/**
 * 主要JavaScript文件 - 处理页面交互和数据更新
 * 白云电气设备数字孪生系统高保真原型
 */

class DigitalTwinSystem {
    constructor() {
        this.isUnityLoaded = false;
        this.unityInstance = null;
        this.charts = {};
        this.realTimeData = {
            voltage: 220.5,
            temperature: 35.2,
            loadRate: 87,
            deviceStatus: {
                transformer: 'running',
                switchA: 'running',
                switchB: 'warning',
                protection: 'running'
            }
        };
        
        this.init();
    }

    /**
     * 初始化系统
     */
    init() {
        this.setupEventListeners();
        this.startTimeDisplay();
        this.startDataSimulation();
        this.initializeCharts();
        this.setupRippleEffect();
        
        console.log('数字孪生系统初始化完成');
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 控制按钮事件
        document.getElementById('overviewBtn')?.addEventListener('click', () => {
            this.handleOverviewClick();
        });

        document.getElementById('tourBtn')?.addEventListener('click', () => {
            this.handleTourClick();
        });

        document.getElementById('expandBtn')?.addEventListener('click', () => {
            this.handleExpandClick();
        });

        // 工具栏按钮事件
        document.querySelectorAll('.toolbar-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handleToolbarClick(e.target);
            });
        });

        // 图表时间范围按钮
        document.querySelectorAll('.chart-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handleChartTimeRange(e.target);
            });
        });

        // 模态框关闭
        document.querySelector('.modal-close')?.addEventListener('click', () => {
            this.closeModal();
        });

        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboard(e);
        });
    }

    /**
     * 启动时间显示
     */
    startTimeDisplay() {
        const updateTime = () => {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            
            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        };

        updateTime();
        setInterval(updateTime, 1000);
    }

    /**
     * 启动数据模拟
     */
    startDataSimulation() {
        setInterval(() => {
            this.updateRealTimeData();
            this.updateMetricCards();
            this.updateDeviceStatus();
        }, 2000);
    }

    /**
     * 更新实时数据
     */
    updateRealTimeData() {
        // 模拟电压波动
        this.realTimeData.voltage += (Math.random() - 0.5) * 2;
        this.realTimeData.voltage = Math.max(215, Math.min(225, this.realTimeData.voltage));

        // 模拟温度变化
        this.realTimeData.temperature += (Math.random() - 0.5) * 1;
        this.realTimeData.temperature = Math.max(30, Math.min(45, this.realTimeData.temperature));

        // 模拟负载率变化
        this.realTimeData.loadRate += (Math.random() - 0.5) * 5;
        this.realTimeData.loadRate = Math.max(60, Math.min(95, this.realTimeData.loadRate));
    }

    /**
     * 更新指标卡片
     */
    updateMetricCards() {
        const cards = document.querySelectorAll('.metric-card');
        
        cards.forEach((card, index) => {
            const valueElement = card.querySelector('.metric-value');
            const trendElement = card.querySelector('.metric-trend');
            
            if (!valueElement) return;

            let newValue, trend;
            
            switch (index) {
                case 0: // 电压
                    newValue = this.realTimeData.voltage.toFixed(1) + 'V';
                    trend = this.realTimeData.voltage > 220 ? 'up' : 'down';
                    break;
                case 1: // 温度
                    newValue = this.realTimeData.temperature.toFixed(1) + '°C';
                    trend = this.realTimeData.temperature > 35 ? 'up' : 'down';
                    break;
                case 2: // 负载率
                    newValue = Math.round(this.realTimeData.loadRate) + '%';
                    trend = this.realTimeData.loadRate > 85 ? 'up' : 'down';
                    break;
            }

            if (newValue) {
                valueElement.textContent = newValue;
                
                // 更新趋势指示器
                if (trendElement) {
                    trendElement.className = `metric-trend ${trend}`;
                    const icon = trendElement.querySelector('i');
                    if (icon) {
                        icon.className = trend === 'up' ? 'fas fa-arrow-up' : 
                                       trend === 'down' ? 'fas fa-arrow-down' : 'fas fa-minus';
                    }
                }
            }
        });
    }

    /**
     * 更新设备状态
     */
    updateDeviceStatus() {
        const deviceItems = document.querySelectorAll('.device-item');
        
        deviceItems.forEach((item, index) => {
            const statusElement = item.querySelector('.device-status');
            if (!statusElement) return;

            // 随机模拟状态变化（低概率）
            if (Math.random() < 0.05) {
                const statuses = ['running', 'warning', 'error'];
                const currentStatus = statusElement.classList.contains('running') ? 'running' :
                                   statusElement.classList.contains('warning') ? 'warning' : 'error';
                
                let newStatus = statuses[Math.floor(Math.random() * statuses.length)];
                
                // 避免错误状态过于频繁
                if (newStatus === 'error' && Math.random() < 0.7) {
                    newStatus = 'running';
                }

                if (newStatus !== currentStatus) {
                    statusElement.className = `device-status ${newStatus}`;
                    statusElement.textContent = this.getStatusText(newStatus);
                    
                    // 如果是警告或错误，显示模态框
                    if (newStatus === 'warning' || newStatus === 'error') {
                        this.showAlert(item.querySelector('.device-name').textContent, newStatus);
                    }
                }
            }
        });
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusTexts = {
            running: '运行中',
            warning: '告警',
            error: '故障'
        };
        return statusTexts[status] || '未知';
    }

    /**
     * 显示告警
     */
    showAlert(deviceName, status) {
        const modal = document.getElementById('alertModal');
        if (!modal) return;

        const alertTitle = modal.querySelector('.alert-title');
        const alertTime = modal.querySelector('.alert-time');
        const alertDesc = modal.querySelector('.alert-desc');

        if (alertTitle) {
            alertTitle.textContent = `${deviceName}${status === 'warning' ? '告警' : '故障'}`;
        }

        if (alertTime) {
            alertTime.textContent = new Date().toLocaleString('zh-CN');
        }

        if (alertDesc) {
            const descriptions = {
                warning: `检测到${deviceName}运行异常，请及时检查设备状态`,
                error: `${deviceName}发生故障，请立即停机检修`
            };
            alertDesc.textContent = descriptions[status] || '设备状态异常';
        }

        modal.style.display = 'block';
        
        // 3秒后自动关闭
        setTimeout(() => {
            this.closeModal();
        }, 3000);
    }

    /**
     * 关闭模态框
     */
    closeModal() {
        const modal = document.getElementById('alertModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    /**
     * 处理总览按钮点击
     */
    handleOverviewClick() {
        console.log('切换到总览视角');
        this.addRippleEffect(event.target);
        
        // 如果Unity已加载，发送消息
        if (this.isUnityLoaded && this.unityInstance) {
            this.unityInstance.SendMessage("Main Camera", "SwitchToOverviewPosition");
        }
        
        // 更新按钮状态
        this.updateControlButtonState('overviewBtn');
    }

    /**
     * 处理漫游按钮点击
     */
    handleTourClick() {
        console.log('开始/停止自动漫游');
        this.addRippleEffect(event.target);
        
        const btn = document.getElementById('tourBtn');
        const isActive = btn.classList.contains('active');
        
        if (isActive) {
            btn.classList.remove('active');
            btn.innerHTML = '<i class="fas fa-route"></i> 自动漫游';
        } else {
            btn.classList.add('active');
            btn.innerHTML = '<i class="fas fa-stop"></i> 停止漫游';
        }
        
        // 如果Unity已加载，发送消息
        if (this.isUnityLoaded && this.unityInstance) {
            this.unityInstance.SendMessage("Main Camera", "ToggleDeviceViewTour");
        }
    }

    /**
     * 处理展开按钮点击
     */
    handleExpandClick() {
        console.log('展开/收起设备');
        this.addRippleEffect(event.target);
        
        const btn = document.getElementById('expandBtn');
        const isExpanded = btn.textContent.includes('收起');
        
        if (isExpanded) {
            btn.innerHTML = '<i class="fas fa-expand-arrows-alt"></i> 设备展开';
        } else {
            btn.innerHTML = '<i class="fas fa-compress-arrows-alt"></i> 设备收起';
        }
        
        // 如果Unity已加载，发送消息
        if (this.isUnityLoaded && this.unityInstance) {
            this.unityInstance.SendMessage("Device", "ToggleExpand");
        }
    }

    /**
     * 处理工具栏按钮点击
     */
    handleToolbarClick(button) {
        const title = button.getAttribute('title');
        console.log(`工具栏操作: ${title}`);
        this.addRippleEffect(button);
        
        switch (title) {
            case '重置视角':
                if (this.isUnityLoaded && this.unityInstance) {
                    this.unityInstance.SendMessage("Main Camera", "ResetView");
                }
                break;
            case '全屏显示':
                this.toggleFullscreen();
                break;
            case '截图':
                this.takeScreenshot();
                break;
        }
    }

    /**
     * 处理图表时间范围切换
     */
    handleChartTimeRange(button) {
        const chartContainer = button.closest('.chart-container');
        const buttons = chartContainer.querySelectorAll('.chart-btn');
        
        buttons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
        
        const timeRange = button.textContent;
        console.log(`切换图表时间范围: ${timeRange}`);
        
        // 这里可以更新图表数据
        this.updateChartData(chartContainer, timeRange);
    }

    /**
     * 更新控制按钮状态
     */
    updateControlButtonState(activeButtonId) {
        const buttons = document.querySelectorAll('.control-btn');
        buttons.forEach(btn => {
            btn.classList.remove('primary');
            if (btn.id === activeButtonId) {
                btn.classList.add('primary');
            }
        });
    }

    /**
     * 添加波纹效果
     */
    addRippleEffect(element) {
        const rect = element.getBoundingClientRect();
        const ripple = document.createElement('span');
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');
        
        element.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    /**
     * 设置波纹效果
     */
    setupRippleEffect() {
        const buttons = document.querySelectorAll('.control-btn, .toolbar-btn, .chart-btn');
        buttons.forEach(button => {
            button.style.position = 'relative';
            button.style.overflow = 'hidden';
        });
    }

    /**
     * 切换全屏
     */
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    /**
     * 截图功能
     */
    takeScreenshot() {
        // 这里可以实现截图功能
        console.log('截图功能');
        // 可以使用html2canvas库来实现
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        // 重新调整图表大小
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.resize) {
                chart.resize();
            }
        });
    }

    /**
     * 处理键盘快捷键
     */
    handleKeyboard(event) {
        // ESC键关闭模态框
        if (event.key === 'Escape') {
            this.closeModal();
        }
        
        // F11切换全屏
        if (event.key === 'F11') {
            event.preventDefault();
            this.toggleFullscreen();
        }
    }

    /**
     * 初始化图表（占位符）
     */
    initializeCharts() {
        // 这个方法将在charts.js中实现
        console.log('图表初始化将在charts.js中完成');
    }

    /**
     * 更新图表数据（占位符）
     */
    updateChartData(container, timeRange) {
        // 这个方法将在charts.js中实现
        console.log(`更新图表数据: ${timeRange}`);
    }

    /**
     * Unity加载完成回调
     */
    onUnityLoaded(unityInstance) {
        this.isUnityLoaded = true;
        this.unityInstance = unityInstance;
        console.log('Unity WebGL加载完成');
        
        // 隐藏加载占位符
        const placeholder = document.querySelector('.unity-placeholder');
        if (placeholder) {
            placeholder.style.display = 'none';
        }
    }
}

// 页面加载完成后初始化系统
document.addEventListener('DOMContentLoaded', () => {
    window.digitalTwinSystem = new DigitalTwinSystem();
});

// 导出类以供其他模块使用
window.DigitalTwinSystem = DigitalTwinSystem;
