// echarts控制器 - 用于在Unity WebGL中控制echarts图表的显示和隐藏

// 在Unity中通过JSLib调用的函数
mergeInto(LibraryManager.library, {
    // 隐藏echarts图表
    HideEchartsInternal: function() {
        console.log("[JS] 隐藏echarts图表");
        try {
            // 获取所有echarts容器
            var chartContainers = document.querySelectorAll('.chart-container');
            
            // 隐藏所有echarts容器
            for (var i = 0; i < chartContainers.length; i++) {
                chartContainers[i].style.display = 'none';
            }
            
            // 也可以单独处理特定的图表
            var chart1 = document.getElementById('chart1');
            var chart2 = document.getElementById('chart2');
            var chart3 = document.getElementById('chart3');
            var chart4 = document.getElementById('chart4');
            
            if (chart1) chart1.style.display = 'none';
            if (chart2) chart2.style.display = 'none';
            if (chart3) chart3.style.display = 'none';
            if (chart4) chart4.style.display = 'none';
            
            console.log("[JS] 已隐藏" + chartContainers.length + "个echarts图表");
        } catch (e) {
            console.error("[JS] 隐藏echarts图表时出错:", e);
        }
    },
    
    // 显示echarts图表
    ShowEchartsInternal: function() {
        console.log("[JS] 显示echarts图表");
        try {
            // 获取所有echarts容器
            var chartContainers = document.querySelectorAll('.chart-container');
            
            // 显示所有echarts容器
            for (var i = 0; i < chartContainers.length; i++) {
                chartContainers[i].style.display = 'block';
            }
            
            // 也可以单独处理特定的图表
            var chart1 = document.getElementById('chart1');
            var chart2 = document.getElementById('chart2');
            var chart3 = document.getElementById('chart3');
            var chart4 = document.getElementById('chart4');
            
            if (chart1) chart1.style.display = 'block';
            if (chart2) chart2.style.display = 'block';
            if (chart3) chart3.style.display = 'block';
            if (chart4) chart4.style.display = 'block';
            
            // 重新调整echarts大小以适应显示
            if (window.echarts) {
                // 如果存在全局echarts实例，重新调整大小
                var charts = [];
                if (window.chart1Instance) charts.push(window.chart1Instance);
                if (window.chart2Instance) charts.push(window.chart2Instance);
                if (window.chart3Instance) charts.push(window.chart3Instance);
                if (window.chart4Instance) charts.push(window.chart4Instance);
                
                // 对所有图表实例调用resize方法
                for (var i = 0; i < charts.length; i++) {
                    if (charts[i] && typeof charts[i].resize === 'function') {
                        charts[i].resize();
                    }
                }
                
                // 触发一次resize事件
                window.dispatchEvent(new Event('resize'));
            }
            
            console.log("[JS] 已显示" + chartContainers.length + "个echarts图表");
        } catch (e) {
            console.error("[JS] 显示echarts图表时出错:", e);
        }
    }
});