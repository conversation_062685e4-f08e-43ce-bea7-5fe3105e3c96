using UnityEngine;
using UnityEditor;

/// <summary>
/// LineConnector组件的自定义编辑器
/// </summary>
[CustomEditor(typeof(LineConnector))]
public class LineConnectorEditor : Editor
{
    private SerializedProperty lineManagerProp;
    private SerializedProperty targetCanvasProp;
    private SerializedProperty lineSortingOrderProp;
    
    private void OnEnable()
    {
        lineManagerProp = serializedObject.FindProperty("lineManager");
        targetCanvasProp = serializedObject.FindProperty("targetCanvas");
        lineSortingOrderProp = serializedObject.FindProperty("lineSortingOrder");
    }
    
    public override void OnInspectorGUI()
    {
        serializedObject.Update();
        
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("连线连接器设置", EditorStyles.boldLabel);
        EditorGUILayout.Space();
        
        // 基本设置
        EditorGUILayout.PropertyField(lineManagerProp, new GUIContent("连线管理器", "引用场景中的LineManager组件"));
        EditorGUILayout.PropertyField(targetCanvasProp, new GUIContent("目标Canvas", "连线所在的Canvas"));
        EditorGUILayout.PropertyField(lineSortingOrderProp, new GUIContent("连线渲染顺序", "连线的渲染顺序，负值使连线显示在其他UI元素后面"));
        
        serializedObject.ApplyModifiedProperties();
        
        // 帮助信息
        EditorGUILayout.Space();
        EditorGUILayout.HelpBox("此组件用于管理3D物体与UI元素之间的连线。", MessageType.Info);
    }
}