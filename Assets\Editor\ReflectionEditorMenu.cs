using UnityEngine;
using UnityEditor;

public class ReflectionEditorMenu : EditorWindow
{
    private static bool enableEditorReflection = true;
    
    [MenuItem("反射效果/设置")]
    public static void ShowWindow()
    {
        GetWindow<ReflectionEditorMenu>("反射效果设置");
    }
    
    [MenuItem("反射效果/刷新反射")]
    public static void RefreshReflection()
    {
        // 强制刷新场景视图，触发反射更新
        if (SceneView.lastActiveSceneView != null)
        {
            SceneView.lastActiveSceneView.Repaint();
        }
        Debug.Log("已刷新反射效果");
    }
    
    [MenuItem("反射效果/启用编辑器反射", true)]
    public static bool ValidateEnableReflection()
    {
        Menu.SetChecked("反射效果/启用编辑器反射", enableEditorReflection);
        return true;
    }
    
    [MenuItem("反射效果/启用编辑器反射")]
    public static void ToggleEnableReflection()
    {
        enableEditorReflection = !enableEditorReflection;
        
        // 通知PlanarReflectionEditor更新状态
        PlanarReflectionEditor.SetEnabled(enableEditorReflection);
        
        // 刷新场景视图
        if (SceneView.lastActiveSceneView != null)
        {
            SceneView.lastActiveSceneView.Repaint();
        }
    }
    
    void OnGUI()
    {
        GUILayout.Label("反射效果设置", EditorStyles.boldLabel);
        
        EditorGUI.BeginChangeCheck();
        enableEditorReflection = EditorGUILayout.Toggle("启用编辑器反射", enableEditorReflection);
        if (EditorGUI.EndChangeCheck())
        {
            // 通知PlanarReflectionEditor更新状态
            PlanarReflectionEditor.SetEnabled(enableEditorReflection);
            
            // 刷新场景视图
            if (SceneView.lastActiveSceneView != null)
            {
                SceneView.lastActiveSceneView.Repaint();
            }
        }
        
        if (GUILayout.Button("刷新反射"))
        {
            RefreshReflection();
        }
        
        EditorGUILayout.HelpBox("启用编辑器反射功能可以在Scene视图中预览反射效果，与Play模式下的效果一致。\n如果编辑器性能下降，可以暂时禁用此功能。", MessageType.Info);
    }
}