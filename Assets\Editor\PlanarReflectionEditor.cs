using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

[InitializeOnLoad]
public class PlanarReflectionEditor
{
    private static Camera sceneCamera;
    private static RenderTexture reflectionTexture;
    private static Dictionary<Material, bool> reflectiveMaterials = new Dictionary<Material, bool>();
    private static bool isInitialized = false;
    private static bool isEnabled = true;
    
    // 构造函数在编辑器启动时调用
    static PlanarReflectionEditor()
    {
        // 订阅场景视图更新事件
        SceneView.duringSceneGui += OnSceneGUI;
        EditorApplication.update += OnEditorUpdate;
        
        // 初始化
        Initialize();
    }
    
    private static void Initialize()
    {
        if (isInitialized) return;
        
        // 查找所有使用ReflectiveGround着色器的材质
        string[] guids = AssetDatabase.FindAssets("t:Material");
        foreach (string guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            Material mat = AssetDatabase.LoadAssetAtPath<Material>(path);
            
            if (mat != null && mat.shader != null && mat.shader.name == "Custom/ReflectiveGround")
            {
                reflectiveMaterials[mat] = true;
                Debug.Log("找到反射材质: " + mat.name);
            }
        }
        
        isInitialized = true;
    }
    
    private static void OnEditorUpdate()
    {
        // 确保场景视图存在
        if (SceneView.lastActiveSceneView == null) return;
        
        // 更新反射纹理
        UpdateReflectionTexture();
    }
    
    private static void OnSceneGUI(SceneView sceneView)
    {
        // 获取场景视图相机
        sceneCamera = sceneView.camera;
        
        // 更新反射纹理
        UpdateReflectionTexture();
        
        // 强制重绘场景视图
        sceneView.Repaint();
    }
    
    // 设置启用状态的公共方法
    public static void SetEnabled(bool enabled)
    {
        isEnabled = enabled;
        Debug.Log("编辑器反射已" + (enabled ? "启用" : "禁用"));
    }
    
    private static void UpdateReflectionTexture()
    {
        if (sceneCamera == null || !isEnabled) return;
        
        // 创建或更新反射纹理
        int width = sceneCamera.pixelWidth;
        int height = sceneCamera.pixelHeight;
        
        if (reflectionTexture == null || reflectionTexture.width != width || reflectionTexture.height != height)
        {
            if (reflectionTexture != null)
                Object.DestroyImmediate(reflectionTexture);
            
            reflectionTexture = new RenderTexture(width, height, 16);
            reflectionTexture.name = "EditorReflectionTexture";
            reflectionTexture.isPowerOfTwo = true;
            reflectionTexture.hideFlags = HideFlags.DontSave;
        }
        
        // 创建反射相机
        Camera reflectionCamera = new GameObject("TempReflectionCamera").AddComponent<Camera>();
        reflectionCamera.CopyFrom(sceneCamera);
        reflectionCamera.enabled = false;
        
        // 设置反射相机参数
        foreach (var kvp in reflectiveMaterials)
        {
            Material mat = kvp.Key;
            if (mat == null) continue;
            
            // 查找使用此材质的对象
            GameObject[] allObjects = Object.FindObjectsOfType<GameObject>();
            foreach (GameObject obj in allObjects)
            {
                Renderer renderer = obj.GetComponent<Renderer>();
                if (renderer != null && renderer.sharedMaterial == mat)
                {
                    // 计算反射矩阵
                    Vector3 pos = obj.transform.position;
                    Vector3 normal = obj.transform.up;
                    float clipPlaneOffset = 0.07f;
                    
                    // 反射矩阵计算
                    float d = -Vector3.Dot(normal, pos) - clipPlaneOffset;
                    Vector4 reflectionPlane = new Vector4(normal.x, normal.y, normal.z, d);
                    Matrix4x4 reflection = CalculateReflectionMatrix(reflectionPlane);
                    
                    // 设置相机位置和矩阵
                    Vector3 oldPos = sceneCamera.transform.position;
                    Vector3 newPos = reflection.MultiplyPoint(oldPos);
                    reflectionCamera.worldToCameraMatrix = sceneCamera.worldToCameraMatrix * reflection;
                    
                    // 设置投影矩阵
                    Vector4 clipPlane = CameraSpacePlane(reflectionCamera, pos, normal, 1.0f, clipPlaneOffset);
                    Matrix4x4 projection = sceneCamera.projectionMatrix;
                    CalculateObliqueMatrix(ref projection, clipPlane);
                    reflectionCamera.projectionMatrix = projection;
                    
                    // 设置相机位置和旋转
                    reflectionCamera.transform.position = newPos;
                    Vector3 euler = sceneCamera.transform.eulerAngles;
                    reflectionCamera.transform.eulerAngles = new Vector3(-euler.x, euler.y, euler.z);
                    
                    // 渲染到纹理
                    reflectionCamera.targetTexture = reflectionTexture;
                    GL.invertCulling = true;
                    reflectionCamera.Render();
                    GL.invertCulling = false;
                    
                    // 设置材质的反射纹理
                    mat.SetTexture("_ReflectionTex", reflectionTexture);
                    break;
                }
            }
        }
        
        // 清理临时相机
        Object.DestroyImmediate(reflectionCamera.gameObject);
    }
    
    // 计算反射矩阵
    private static Matrix4x4 CalculateReflectionMatrix(Vector4 plane)
    {
        Matrix4x4 reflectionMat = Matrix4x4.zero;
        
        reflectionMat.m00 = (1F - 2F * plane[0] * plane[0]);
        reflectionMat.m01 = (-2F * plane[0] * plane[1]);
        reflectionMat.m02 = (-2F * plane[0] * plane[2]);
        reflectionMat.m03 = (-2F * plane[3] * plane[0]);

        reflectionMat.m10 = (-2F * plane[1] * plane[0]);
        reflectionMat.m11 = (1F - 2F * plane[1] * plane[1]);
        reflectionMat.m12 = (-2F * plane[1] * plane[2]);
        reflectionMat.m13 = (-2F * plane[3] * plane[1]);

        reflectionMat.m20 = (-2F * plane[2] * plane[0]);
        reflectionMat.m21 = (-2F * plane[2] * plane[1]);
        reflectionMat.m22 = (1F - 2F * plane[2] * plane[2]);
        reflectionMat.m23 = (-2F * plane[3] * plane[2]);

        reflectionMat.m30 = 0F;
        reflectionMat.m31 = 0F;
        reflectionMat.m32 = 0F;
        reflectionMat.m33 = 1F;
        
        return reflectionMat;
    }
    
    // 计算相机空间中的平面
    private static Vector4 CameraSpacePlane(Camera cam, Vector3 pos, Vector3 normal, float sideSign, float clipPlaneOffset)
    {
        Vector3 offsetPos = pos + normal * clipPlaneOffset;
        Matrix4x4 m = cam.worldToCameraMatrix;
        Vector3 cpos = m.MultiplyPoint(offsetPos);
        Vector3 cnormal = m.MultiplyVector(normal).normalized * sideSign;
        return new Vector4(cnormal.x, cnormal.y, cnormal.z, -Vector3.Dot(cpos, cnormal));
    }
    
    // 计算斜投影矩阵
    private static void CalculateObliqueMatrix(ref Matrix4x4 projection, Vector4 clipPlane)
    {
        Vector4 q = projection.inverse * new Vector4(
            Mathf.Sign(clipPlane.x),
            Mathf.Sign(clipPlane.y),
            1.0f,
            1.0f
        );
        Vector4 c = clipPlane * (2.0F / Vector4.Dot(clipPlane, q));
        projection[2] = c.x - projection[3];
        projection[6] = c.y - projection[7];
        projection[10] = c.z - projection[11];
        projection[14] = c.w - projection[15];
    }
}