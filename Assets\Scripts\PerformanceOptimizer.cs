using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 性能优化器 - 用于优化大型模型的性能
/// </summary>
[AddComponentMenu("设备交互/性能优化器")]
public class PerformanceOptimizer : MonoBehaviour
{
    [Header("LOD设置")]
    [Tooltip("是否启用LOD系统")]
    public bool enableLOD = true;
    
    [Tooltip("LOD切换距离")]
    public float lodDistance = 5.0f;
    
    [Tooltip("远距离LOD简化比例 (0-1)")]
    [Range(0.1f, 0.9f)]
    public float simplificationRatio = 0.5f;
    
    [Header("渲染优化")]
    [Tooltip("是否启用静态批处理")]
    public bool enableStaticBatching = true;
    
    [Tooltip("是否启用GPU实例化")]
    public bool enableGPUInstancing = true;
    
    [Tooltip("是否启用遮挡剔除")]
    public bool enableOcclusionCulling = true;
    
    [Header("组件优化")]
    [Tooltip("非交互组件列表 - 这些组件将被合并或简化")]
    public List<Transform> nonInteractiveComponents = new List<Transform>();
    
    [Tooltip("是否在运行时禁用非必要碰撞体")]
    public bool disableUnnecessaryColliders = true;
    
    [Header("监控")]
    [Tooltip("是否显示性能监控")]
    public bool showPerformanceMonitor = true;
    
    [Tooltip("性能警告阈值 (FPS)")]
    public int performanceWarningThreshold = 30;
    
    // 私有变量
    private float frameRate;
    private int frameCount;
    private float timePassed;
    private Dictionary<Transform, MeshFilter> originalMeshes = new Dictionary<Transform, MeshFilter>();
    private Dictionary<Transform, MeshFilter> simplifiedMeshes = new Dictionary<Transform, MeshFilter>();
    
    void Start()
    {
        // 初始化性能监控
        frameCount = 0;
        timePassed = 0f;
        
        // 应用静态批处理设置
        if (enableStaticBatching)
        {
            ApplyStaticBatching();
        }
        
        // 应用GPU实例化设置
        if (enableGPUInstancing)
        {
            ApplyGPUInstancing();
        }
        
        // 应用遮挡剔除设置
        if (enableOcclusionCulling)
        {
            ApplyOcclusionCulling();
        }
        
        // 禁用非必要碰撞体
        if (disableUnnecessaryColliders)
        {
            DisableUnnecessaryColliders();
        }
        
        // 准备LOD系统
        if (enableLOD)
        {
            PrepareLODSystem();
        }
    }
    
    void Update()
    {
        // 更新帧率计算
        frameCount++;
        timePassed += Time.deltaTime;
        
        if (timePassed >= 1.0f)
        {
            frameRate = frameCount / timePassed;
            frameCount = 0;
            timePassed = 0f;
            
            // 检查性能警告
            if (frameRate < performanceWarningThreshold)
            {
                Debug.LogWarning($"性能警告：当前帧率 {frameRate:F1} FPS，低于阈值 {performanceWarningThreshold} FPS");
            }
        }
        
        // 处理LOD切换
        if (enableLOD)
        {
            UpdateLOD();
        }
    }
    
    void OnGUI()
    {
        // 显示性能监控
        if (showPerformanceMonitor)
        {
            GUI.Label(new Rect(10, 10, 200, 20), $"FPS: {frameRate:F1}");
            GUI.Label(new Rect(10, 30, 200, 20), $"渲染时间: {(Time.deltaTime * 1000):F1} ms");
        }
    }
    
    /// <summary>
    /// 应用静态批处理
    /// </summary>
    private void ApplyStaticBatching()
    {
        foreach (Transform component in nonInteractiveComponents)
        {
            if (component != null)
            {
                // 标记为静态以启用批处理
                component.gameObject.isStatic = true;
            }
        }
    }
    
    /// <summary>
    /// 应用GPU实例化
    /// </summary>
    private void ApplyGPUInstancing()
    {
        // 查找具有相同材质的渲染器
        Dictionary<Material, List<Renderer>> materialGroups = new Dictionary<Material, List<Renderer>>();
        
        foreach (Transform component in nonInteractiveComponents)
        {
            if (component != null)
            {
                Renderer renderer = component.GetComponent<Renderer>();
                if (renderer != null && renderer.sharedMaterial != null)
                {
                    Material material = renderer.sharedMaterial;
                    
                    if (!materialGroups.ContainsKey(material))
                    {
                        materialGroups[material] = new List<Renderer>();
                        
                        // 启用材质的GPU实例化
                        material.enableInstancing = true;
                    }
                    
                    materialGroups[material].Add(renderer);
                }
            }
        }
    }
    
    /// <summary>
    /// 应用遮挡剔除
    /// </summary>
    private void ApplyOcclusionCulling()
    {
        // 为大型组件添加遮挡剔除组件
        foreach (Transform component in nonInteractiveComponents)
        {
            if (component != null)
            {
                Renderer renderer = component.GetComponent<Renderer>();
                if (renderer != null)
                {
                    // 启用遮挡剔除
                    renderer.allowOcclusionWhenDynamic = true;
                }
            }
        }
    }
    
    /// <summary>
    /// 禁用非必要碰撞体
    /// </summary>
    private void DisableUnnecessaryColliders()
    {
        foreach (Transform component in nonInteractiveComponents)
        {
            if (component != null)
            {
                Collider collider = component.GetComponent<Collider>();
                if (collider != null)
                {
                    // 禁用非交互组件的碰撞体
                    collider.enabled = false;
                }
            }
        }
    }
    
    /// <summary>
    /// 准备LOD系统
    /// </summary>
    private void PrepareLODSystem()
    {
        // 为每个非交互组件创建简化网格
        foreach (Transform component in nonInteractiveComponents)
        {
            if (component != null)
            {
                MeshFilter meshFilter = component.GetComponent<MeshFilter>();
                if (meshFilter != null && meshFilter.sharedMesh != null)
                {
                    // 保存原始网格
                    originalMeshes[component] = meshFilter;
                    
                    // 创建简化网格
                    Mesh simplifiedMesh = SimplifyMesh(meshFilter.sharedMesh, simplificationRatio);
                    
                    // 创建新的MeshFilter用于LOD切换
                    GameObject simplifiedObj = new GameObject(component.name + "_LOD");
                    simplifiedObj.transform.SetParent(component.parent);
                    simplifiedObj.transform.localPosition = component.localPosition;
                    simplifiedObj.transform.localRotation = component.localRotation;
                    simplifiedObj.transform.localScale = component.localScale;
                    
                    MeshFilter simplifiedMeshFilter = simplifiedObj.AddComponent<MeshFilter>();
                    simplifiedMeshFilter.sharedMesh = simplifiedMesh;
                    
                    MeshRenderer simplifiedRenderer = simplifiedObj.AddComponent<MeshRenderer>();
                    Renderer originalRenderer = component.GetComponent<Renderer>();
                    if (originalRenderer != null)
                    {
                        simplifiedRenderer.sharedMaterials = originalRenderer.sharedMaterials;
                    }
                    
                    // 初始时禁用简化对象
                    simplifiedObj.SetActive(false);
                    
                    // 保存简化网格引用
                    simplifiedMeshes[component] = simplifiedMeshFilter;
                }
            }
        }
    }
    
    /// <summary>
    /// 更新LOD状态
    /// </summary>
    private void UpdateLOD()
    {
        if (Camera.main == null) return;
        
        foreach (Transform component in nonInteractiveComponents)
        {
            if (component != null && originalMeshes.ContainsKey(component) && simplifiedMeshes.ContainsKey(component))
            {
                float distance = Vector3.Distance(Camera.main.transform.position, component.position);
                
                // 根据距离切换LOD
                if (distance > lodDistance)
                {
                    // 远距离 - 使用简化网格
                    component.gameObject.SetActive(false);
                    simplifiedMeshes[component].transform.gameObject.SetActive(true);
                }
                else
                {
                    // 近距离 - 使用原始网格
                    component.gameObject.SetActive(true);
                    simplifiedMeshes[component].transform.gameObject.SetActive(false);
                }
            }
        }
    }
    
    /// <summary>
    /// 简化网格
    /// </summary>
    private Mesh SimplifyMesh(Mesh originalMesh, float ratio)
    {
        // 注意：实际项目中应使用网格简化库，如UnityMeshSimplifier
        // 这里仅作为示例，创建一个简单的简化版本
        Mesh simplifiedMesh = new Mesh();
        
        // 复制原始网格数据
        simplifiedMesh.vertices = originalMesh.vertices;
        simplifiedMesh.triangles = originalMesh.triangles;
        simplifiedMesh.normals = originalMesh.normals;
        simplifiedMesh.uv = originalMesh.uv;
        
        // 简化处理 - 在实际项目中替换为真正的网格简化算法
        // 这里仅减少三角形数量作为示例
        int[] triangles = originalMesh.triangles;
        int targetTriangleCount = Mathf.FloorToInt(triangles.Length / 3 * ratio);
        int[] simplifiedTriangles = new int[targetTriangleCount * 3];
        
        for (int i = 0; i < targetTriangleCount * 3; i++)
        {
            simplifiedTriangles[i] = triangles[i];
        }
        
        simplifiedMesh.triangles = simplifiedTriangles;
        simplifiedMesh.RecalculateBounds();
        simplifiedMesh.RecalculateNormals();
        
        return simplifiedMesh;
    }
}