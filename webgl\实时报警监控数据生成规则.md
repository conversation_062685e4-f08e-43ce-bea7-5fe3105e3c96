# 实时报警监控数据生成规则文档

## 概述
本文档描述了桂林智源SVG数字化系统中实时报警监控模块的数据生成规则，用于模拟真实的设备运行状态和事件记录。

## 事件类型定义

### 1. 报警事件 (alarm)
- **颜色标识**: 黄色 (#ffaa00)
- **权重**: 30%
- **描述**: 需要关注但不影响系统正常运行的警告信息
- **示例**: 温度偏高、参数偏离正常范围、性能下降等

### 2. 故障事件 (fault)
- **颜色标识**: 红色 (#ff4444)
- **权重**: 20%
- **描述**: 影响系统正常运行的严重问题
- **示例**: 设备故障、通信中断、保护动作等

### 3. 恢复正常事件 (recovery)
- **颜色标识**: 白色 (#ffffff)
- **权重**: 50%
- **描述**: 系统恢复正常或完成维护操作的信息
- **示例**: 故障恢复、参数调整完成、系统自检通过等

## 数据结构

### 事件记录格式
```javascript
{
  timestamp: Date,        // 事件发生时间
  type: String,          // 事件类型: 'alarm', 'fault', 'recovery'
  device: String,        // 设备名称
  message: String        // 事件描述
}
```

### 时间格式
- **显示格式**: YYYY-MM-DD HH:mm:ss
- **示例**: 2025-06-25 14:30:25
- **排序**: 按时间戳降序排列（最新事件在前）

## 设备列表

系统监控的设备包括：
- SVG主控
- IGBT模块
- 水冷系统
- 通信模块
- 电压调节
- 功率监测
- 电源模块
- 系统控制
- 直流母线
- 变压器
- 电抗器
- 电容器组
- 风机系统
- 温度监测
- 保护装置

## 事件消息库

### 报警事件消息
- 水冷系统温度偏高
- 功率因数略低
- 电压波动检测
- 通信延迟增加
- 负载不平衡警告
- 温度传感器异常
- 电流谐波超标
- 系统响应时间延长
- 设备运行时间过长
- 环境温度升高
- 直流母线电压偏低
- IGBT温度接近上限
- 冷却液流量不足
- 电抗器温度偏高
- 变压器负载率高
- 电容器容量下降
- 风机转速异常
- 绝缘电阻偏低
- 谐波含量超标
- 功率输出不稳定
- 控制精度下降
- 响应速度变慢
- 数据采集延迟
- 备用设备预警
- 维护周期临近

### 故障事件消息
- SVG模块通信中断
- 水冷系统严重故障
- 电压保护动作
- 过流保护触发
- 温度保护启动
- 通信链路完全中断
- 传感器读取失败
- 控制器响应超时
- 电源模块异常
- 系统紧急停机
- IGBT模块过热故障
- 直流母线电压异常
- 交流接触器故障
- 冷却泵电机故障
- 变压器绝缘故障
- 电抗器过载保护
- 控制电源故障
- 光纤通信中断
- 主控板硬件故障
- 风机系统故障
- 绝缘监测报警
- 接地故障检测
- 谐波滤波器故障
- 电容器组故障
- 断路器拒动故障
- 保护装置异常
- 测量回路断线
- 操作回路故障
- 辅助电源故障
- 人机界面通信故障

### 恢复正常事件消息
- 系统运行状态正常
- SVG模块参数调整完成
- 水冷系统运行稳定
- 电压电流参数正常
- 功率因数调节完成
- 通信状态良好
- 设备温度正常
- 系统自检通过
- 负载平衡调整完成
- 数据采集正常
- 故障恢复，设备正常运行
- 参数调整完成，系统稳定
- 维护作业完成
- 设备重启成功

## 生成规则

### 时间间隔
- **基础间隔**: 5秒
- **计算公式**: 5000毫秒

### 事件生成流程
1. 根据权重随机选择事件类型
2. 从设备列表中随机选择设备
3. 根据事件类型从对应消息库中随机选择消息
4. 生成当前时间戳
5. 创建事件记录并添加到显示列表

### 显示限制
- **最大显示条数**: 15条
- **超出处理**: 自动删除最旧的记录
- **排序方式**: 按时间戳降序排列

## 筛选功能

### 筛选选项
- **所有事件**: 显示全部事件记录
- **实时记录**: 显示最近的事件记录
- **报警事件**: 仅显示报警类型事件
- **故障事件**: 仅显示故障类型事件

## 维护说明

### 添加新设备
在 `devices` 数组中添加新的设备名称。

### 添加新事件消息
在对应的事件消息数组中添加新的消息文本：
- `alarmEvents`: 报警事件消息
- `faultEvents`: 故障事件消息  
- `recoveryEvents`: 恢复正常事件消息

### 调整事件权重
修改 `eventTypes` 数组中的权重值：
```javascript
const eventTypes = [
  { type: 'recovery', weight: 50 },  // 50%
  { type: 'alarm', weight: 30 },     // 30%
  { type: 'fault', weight: 20 }      // 20%
];
```

### 调整生成频率
修改 `startAlarmSimulation()` 函数中的时间间隔参数。

## 技术实现

### 核心函数
- `addAlarmLog(type, device, message, timestamp)`: 添加报警日志
- `generateRandomAlarmEvent()`: 生成随机事件
- `filterAlarmList(filter)`: 筛选事件列表
- `initializeAlarmLog()`: 初始化历史记录

### CSS类名映射
- `.alarm-item.alarm`: 报警事件样式
- `.alarm-item.fault`: 故障事件样式
- `.alarm-item.recovery`: 恢复事件样式

---

**文档版本**: v1.0  
**创建日期**: 2025-06-25  
**维护人员**: 桂林智源技术团队
